import type { RouteRecordRaw } from 'vue-router';
import { $t } from '#/locales';
import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'lucide:settings',
      // order: -1,
      title: $t('page.platform.title'),
    },
    name: 'Platform',
    path: '/platform',
    children: [
      // 账号管理
      {
        name: 'AccountManage',
        path: 'account_manage',
        component: () => import('#/views/platform/accountmanage/index.vue'),
        meta: {
          title: $t('page.platform.account_manage'),
        },
      },
      // 入口管理
      {
        name: '入口管理',
        path: 'enter_manage',
        component: () => import('#/views/platform/enter_manage/index.vue'),
        meta: {
          title: $t('page.platform.enter_manage'),
        },
      },
      // 权限管理
      {
        name: '权限管理',
        path: 'permission_manage',
        component: () => import('#/views/platform/permission_manage/index.vue'),
        meta: {
          title: $t('page.platform.permission_manage'),
        },
      },
      // 添加角色组
      {
        path: 'permission_manage/add_rolegroup',
        // path: 'add_rolegroup',
        component: () =>
          import('#/views/platform/permission_manage/add_rolegroup.vue'),
        name: 'add_rolegroup',
        meta: {
          title: $t('page.platform.add_rolegroup'),
          hideInMenu: true,
          // roles: PERM.platform.gain,
          // paltallows: PERM.platform.platform
        },
      },
      {
        name: 'system_tool',
        path: '/platform/tools',
        component: () => import('#/views/platform/tools/index.vue'),
        meta: {
          title: '系统工具',
        },
      },
      {
        name: 'image_check',
        path: '/platform/tools/image_check',
        component: () => import('#/views/platform/tools/image.vue'),
        meta: {
          hideInMenu: true,
          title: '图片自测',
        },
      },
      {
        name: 'content_check',
        path: '/platform/tools/content_check',
        component: () => import('#/views/platform/tools/content.vue'),
        meta: {
          hideInMenu: true,
          title: '图片自测',
        },
      },

      {
        name: 'CriteriaManage',
        path: 'label_manage_view',
        component: () => import('#/views/platform/criteria-manage.vue'),
        meta: {
          title: $t('page.platform.criteriaManage'),
        },
      },
      {
        path: 'label_manage',
        component: () => import('#/views/platform/label-manage.vue'),
        name: 'LabelManage',
        meta: { title: $t('page.platform.labelManage') },
      },
      {
        name: 'file_download',
        path: '/platform/tools/file_download',
        component: () => import('#/views/platform/tools/file-download.vue'),
        meta: {
          hideInMenu: true,
          title: '文件下载',
        },
      },
      {
        path: 'supplier',
        component: () => import('#/views/platform/supplier.vue'),
        name: 'supplier',
        meta: { title: $t('page.platform.supplier') },
      },
      {
        name: 'key_to_file',
        path: '/platform/tools/key_to_file',
        component: () => import('#/views/platform/tools/key-to-file.vue'),
        meta: {
          hideInMenu: true,
          title: 'key转链接',
        },
      },
      {
        name: 'dev_control',
        path: '/platform/tools/dev_control',
        component: () => import('#/views/platform/tools/dev-control.vue'),
        meta: {
          hideInMenu: true,
          title: '分支管理',
        },
      },
      {
        name: 'reset_double_fator',
        path: '/platform/tools/reset_double_fator',
        component: () =>
          import('#/views/platform/tools/reset-double_fator.vue'),
        meta: {
          hideInMenu: true,
          title: '重置双因子',
        },
      },
      {
        name: 'worklog_download',
        path: '/platform/tools/worklog_download',
        component: () => import('#/views/platform/tools/worklog-download.vue'),
        meta: {
          hideInMenu: true,
          title: '文件协作记录下载',
        },
      },
      {
        name: 'strategy',
        path: '/platform/tools/strategy',
        component: () => import('#/views/platform/tools/strategy.vue'),
        meta: {
          hideInMenu: true,
          title: '策略拦截',
        },
      },
      {
        name: 'img_split_test',
        path: '/platform/tools/img_split_test',
        component: () => import('#/views/platform/tools/img-spilt-test.vue'),
        meta: {
          hideInMenu: true,
          title: '策略拦截',
        },
      },
      {
        name: 'query_company',
        path: '/platform/tools/query_company',
        component: () => import('#/views/platform/tools/query-company.vue'),
        meta: {
          hideInMenu: true,
          title: '策略拦截',
        },
      },
      {
        name: 'model_judge',
        path: '/platform/tools/model_judge',
        component: () => import('#/views/platform/tools/model-judge.vue'),
        meta: {
          hideInMenu: true,
          title: '大模型评估',
        },
      },
      {
        name: 'video_iframe',
        path: '/platform/tools/video_iframe',
        component: () => import('#/views/platform/tools/video-iframe.vue'),
        meta: {
          hideInMenu: true,
          title: '视频抽帧',
        },
      },
      {
        name: 'image_mark',
        path: '/platform/tools/image_mark',
        component: () => import('#/views/platform/tools/image-mark.vue'),
        meta: {
          hideInMenu: true,
          title: '标记图片上传',
        },
      },
      {
        name: 'supplierimg_view',
        path: '/platform/tools/supplierimg_view',
        component: () => import('#/views/platform/tools/supplierimg-view.vue'),
        meta: {
          hideInMenu: true,
          title: '视频抽帧',
        },
      },
      {
        name: 'record_video',
        path: '/platform/tools/record_video',
        component: () => import('#/views/platform/tools/record-video.vue'),
        meta: {
          hideInMenu: true,
          title: '录屏查看',
        },
      },
      {
        path: 'fault_isolation',
        component: () => import('#/views/platform/fault-isolation.vue'),
        name: 'fault_isolation',
        meta: { title: $t('page.platform.fault_isolation') },
      },
      // 登录日志
      {
        path: 'get_login_log',
        component: () => import('#/views/platform/login-log.vue'),
        name: 'get_login_log',
        meta: { title: $t('page.platform.get_login_log') },
      },
      // 审计日志
      {
        path: 'audit_log',
        component: () => import('#/views/platform/audit-log.vue'),
        name: 'audit_log',
        meta: { title: $t('page.platform.audit_log') },
      },
      // 套壳代理白名单
      {
        path: 'electronWhiteList',
        component: () => import('#/views/platform/proxy-white-list.vue'),
        name: 'electronWhiteList',
        meta: { title: $t('page.platform.electronWhiteList') },
      },
      {
        path: 'facial_confirm',
        component: () => import('#/views/platform/facialConfirm/index.vue'),
        name: 'facialConfirm',
        meta: {
          title: $t('page.platform.facialConfirm'),
          // roles: PERM.platform.gain,
          // paltallows: PERM.platform.platform
        },
      },
      {
        path: 'facial_confirm_list',
        component: () => import('#/views/platform/facialConfirm/list.vue'),
        name: 'facialConfirmList',
        meta: {
          title: $t('page.platform.facialConfirmList'),
          // roles: PERM.platform.gain,
          // paltallows: PERM.platform.platform
        },
      },
      {
        name: 'label-map',
        path: '/platform/label_map',
        component: () => import('#/views/platform/label_map/index.vue'),
        meta: {
          title: $t('page.platform.label_map'),
        },
      },
      {
        path: 'app_firewall_approval',
        component: () => import('#/views/platform/firewall-approval/index.vue'),
        name: 'appFirewallApproval',
        meta: {
          title: '防火墙应用审批',
        },
      },
    ],
  },
];

export default routes;
