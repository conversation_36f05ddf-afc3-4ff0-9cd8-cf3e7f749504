<script setup lang="ts">
import { onMounted, ref, watch, useTemplateRef } from 'vue';

import { httpsCheck } from '@vben/utils';

import { message } from 'ant-design-vue';
import { storeToRefs } from 'pinia';

import { audioDetail, getUrlData, videoDetail } from '#/api/preview';
import ImgView from '#/components/image/index.vue';
import { MediaType } from '#/composables/task/use-task-media-type';
import {
  useLoadingState,
  ViewStatus,
} from '#/composables/ui/use-loading-state';
import { FRAME_CHECK_TYPE_MAP } from '#/constants/maps';
import { useTaskStore } from '#/store';


const props = defineProps<{
  audioUrls: string[];
  mediaType: MediaType;
  videoUrls: {
    ism3u8: boolean;
    src: string;
  }[];
}>();
const emit = defineEmits<{
  (e: 'loaded'): void;
  (e: 'loadFail'): void;
}>();
const frameDetail = ref<any[]>([]);
const ocrText = ref<any[]>([]);
const asrText = ref<any[]>([]);
const audioDetailData = ref<any[]>([]);
const { taskItem, biz } = storeToRefs(useTaskStore());
const audioUrl = ref<string>('');
const videoUrls0 = ref<string[]>([]);
const fileName = ref<string>('');
const { isLoading, setLoading, setLoaded, setLoadFailed } =
  useLoadingState(ViewStatus.UNLOAD);
const getVideoDetail = async () => {
  const resp = await videoDetail({
    fileinx: taskItem.value?.fileinx || '',
    scene: '',
    from: biz.value,
    force: false,
    id_new: false,
    nowtime: taskItem.value?.modify_time,
  });
  if (resp.result) {
    frameDetail.value = resp.frameDetail;
    ocrText.value = resp.ocr_text;
    asrText.value = resp.asr_text;
    fileName.value = resp.fname;
    const videoResp = await getUrlData(httpsCheck(resp.video_url));
    videoUrls0.value = videoResp.videos;
    setLoaded();
    emit('loaded');
    return;
  }
  setLoadFailed();
  emit('loadFail');
  message.warning('无法获取视频详细信息');
};
const audioPlayers = useTemplateRef<HTMLAudioElement>('audioPlayers');
const getAudioDetail = async () => {
  const resp = await audioDetail({
    fileinx: taskItem.value?.fileinx || '',
    scene: '',
    from: biz.value,
    force: false,
    id_new: false,
    nowtime: taskItem.value?.modify_time,
  });
  if (resp.result) {
    audioDetailData.value = resp.audioDetail;
    asrText.value = resp.asr_text;
    audioUrl.value = httpsCheck(resp.audio_url);
    fileName.value = resp.fname;
    if (resp.audio_url) {
      const audioResp = await getUrlData(httpsCheck(resp.audio_url));
      console.log('audioResp', audioResp);
    }
    setLoaded();
    emit('loaded');
    return;
  }
  setLoadFailed();
  emit('loadFail');
  message.warning('无法获取音频详细信息');
};
const setup = async () => {
  setLoading();
  try {
    await (props.mediaType === MediaType.Video
      ? getVideoDetail()
      : getAudioDetail());
  } catch {
    setLoadFailed();
    emit('loadFail');
    message.error('加载失败');
  }
};
watch(taskItem, () => {
  frameDetail.value = [];
  ocrText.value = [];
  asrText.value = [];
  audioDetailData.value = [];
  videoUrls0.value = [];
  setup();
});
const handleWaiting = (e: Event) => {
  console.log('waiting',e);
};
const handleLoadedMetadata = () => {
  console.log('loadedmetadata');
};
const handleStalled = () => {
  console.log('stalled');
};
const handleSuspend = () => {
  console.log('suspend');
};
const handleEmptied = () => {
  console.log('emptied');
};
const handleCanplay = () => {
  console.log('canplay');
};
const handleCanplaythrough = () => {
  console.log('canplaythrough');
};
const handleError = () => {
  console.log('error');
};

onMounted(() => {
  console.log('audioPlayers', audioPlayers.value);
  setup();
});
</script>
<template>
  <div class="flex" v-loading="{ active: isLoading, text: '拼命加载中...' }">
    <div class="flex max-h-[calc(100vh-16rem)] gap-2 overflow-auto">
      <div class="flex p-2">
        <!-- <m3u8-player
            v-if="item.ism3u8"
            style="width: 350px"
            :url="item.src"
            ref="m3u8player"
          ></m3u8-player> -->
        <template v-if="mediaType === MediaType.Video">
          <video
            v-for="(item, index) in videoUrls0"
            :key="index"
            :src="item"
            class="h-[300px] w-[350px] object-cover"
            controls
            controlslist="nodownload"
            disablePictureInPicture
          >
            对不起，您的浏览器不支持HTML5视频。
          </video>
        </template>
        <template v-if="mediaType === MediaType.Audio">
          <!-- 使用从API获取的音频URL -->
          <audio
            v-if="audioUrl"
            controls
            controlslist="nodownload"
            disablePictureInPicture
            class="mt-2"
          >
            <source :src="audioUrl" type="audio/mpeg" />
            您的浏览器不支持音频播放
          </audio>
          <!-- 使用props传入的音频URL列表 -->
          <div v-else class="flex flex-col gap-2 mt-2">
            <audio
              v-for="(item, index) in audioUrls"
              :key="index"
              ref="audioPlayers"
              :src="item"
              controls
              controlslist="nodownload"
              @waiting="handleWaiting"
              @loadedmetadata="handleLoadedMetadata"
              @stalled="handleStalled"
              @suspend="handleSuspend"
              @emptied="handleEmptied"
              @canplay="handleCanplay"
              @canplaythrough="handleCanplaythrough"
              @error="handleError"
              disablePictureInPicture
            >

              您的浏览器不支持音频播放
            </audio>
          </div>
        </template>
        <div
          v-if="frameDetail && frameDetail.length > 0"
          class="ml-4 mt-2 flex flex-wrap gap-2"
        >
          <div
            v-for="(item, index) in frameDetail"
            :key="index"
            class="relative"
          >
            <img-view
              :src="httpsCheck(item.imgUrl)"
              alt="frame"
              class="h-[300px] w-[200px] object-cover"
            />
            <div
              class="absolute left-0 top-0 bg-red-500 p-1 text-xs text-white"
            >
              {{ FRAME_CHECK_TYPE_MAP[item.imgType] }}
            </div>
            <div class="absolute right-0 top-0 bg-white p-1 text-xs text-black">
              {{ new Date(item.time * 1000).toISOString().slice(11, 19) }}
            </div>
          </div>
        </div>
      </div>

      <div class="mt-2 flex flex-col gap-2">
        <a-card v-if="ocrText && ocrText.length > 0">
          <div v-for="(item, index) in ocrText" :key="index">
            <a-card class="max-h-[300px] w-[300px] overflow-auto">
              <a-card-meta
                :title="new Date(item.time * 1000).toISOString().slice(11, 19)"
              >
                <template #description>
                  {{ item.text }}
                </template>
              </a-card-meta>
            </a-card>
          </div>
        </a-card>
        <a-card>
          <div v-for="(item, index) in asrText" :key="index">
            <a-card class="max-h-[300px] w-[300px] overflow-auto">
              <a-card-meta
                :title="new Date(item.time * 1000).toISOString().slice(11, 19)"
              >
                <template #description>
                  {{ item.text }}
                </template>
              </a-card-meta>
            </a-card>
          </div>
        </a-card>
      </div>
    </div>
  </div>
</template>
