<script lang="ts" setup>
import { getPreviewImageInfo } from '#/api/task';
import { useTaskStore } from '#/store';
import { storeToRefs } from 'pinia';
import { watch, ref, computed } from 'vue';
import WordLevelBox from './WordLevelBox.vue';
import ImgView from '#/components/image/index.vue';
import Field from '#/components/common/Field.vue';
import useForbidStore from '#/store/forbid';
import { MACHINE_IMG_LABEL_MAP } from '#/constants/maps/task';
import { getExpireUrlBatch } from '#/api/preview';
import usePlatformStore from '#/store/platform';
const platformStore = usePlatformStore();
// 控制显示全部图片的Modal
const showAllImagesModal = ref(false);
// 最大显示图片数
const MAX_DISPLAY_IMAGES = 9;

const taskStore = useTaskStore();
const { taskItem } = storeToRefs(taskStore);
const ai_label_desc = ref<string>('');
const forbidStore = useForbidStore();
const imgInfos = ref<any[]>([]);
const processedImgInfos = computed(() => {
  return imgInfos.value.map(imgInfo => {
    const labels = processLabel(imgInfo.label);
    
    // 复制标签数组以便修改
    let finalLabels = [...labels];
    
    // 确定图片边框颜色
    let borderColor = '';
    if (imgInfo.is_reco_hit) {
      borderColor = 'red';
    } else if (imgInfo.is_ocr_hit) {
      borderColor = 'blue';
    } else if (imgInfo.is_qr_code_hit) {
      borderColor = 'yellow';
      // 为二维码添加特殊标签
      finalLabels.push('二维码');
    }
    
    return {
      ...imgInfo,
      processedLabels: finalLabels,
      hasLabels: finalLabels.length > 0,
      borderColor: borderColor
    };
  });
});
const getPreviewImageInfoData = async () => {
  if(!taskItem.value) {
    return;
  }
  const res = await getPreviewImageInfo({
    from: taskItem.value?.biz,
    fsha: taskItem.value?.fsha,
    scene: taskItem.value?.scene,
    extra_id: taskItem.value?.extra_id,
    fileinx: taskItem.value?.fileinx,
  });
  if (!res.result || !res.data || !res.data.images) {
    imgInfos.value = [];
    return;
  }
   // 获取图片缩放配置
   const imageScaleConfig = platformStore.currentQueueConfig?.image_scale_config || {};
  const scale_width = imageScaleConfig.scale_width;
  const scale_height = imageScaleConfig.scale_height;
  
  // 准备批量获取过期URL的参数
  const items = res.data.images.map((item: any) => ({
    key: item.key,
    bucket: item.bucket || '',
    mode: "image_scale",
    image_scale_width: scale_width,
    image_scale_height: scale_height,
  }));
  // 如果有图片，则请求刷新过期时间
  if (items.length > 0) {
    try {
      const expireUrlBatchRes = await getExpireUrlBatch(items);
      
      // 如果成功获取了新的URL，更新图片信息
      if (expireUrlBatchRes.result && expireUrlBatchRes.data && expireUrlBatchRes.data.items) {
        // 创建URL映射表
        const urlMap = expireUrlBatchRes.data.items.reduce((map: Record<string, string>, item: any) => {
          if (item.key && item.url) {
            map[item.key] = item.url;
          }
          return map;
        }, {});
        
        // 更新图片信息中的URL
        res.data.images = res.data.images.map((item: any) => {
          if (item.url && urlMap[item.url]) {
            return { ...item, url: urlMap[item.url] };
          }
          return item;
        });
      }
    } catch (error) {
      console.error("获取图片过期URL失败:", error);
    }
  }
  console.log("机审图片",res.data)
  imgInfos.value = res.data.images;
  ai_label_desc.value = res.data.ai_label_desc;
};
const hasOcrKeywords = computed(() => {
  return (
    taskItem.value?.external_json?.ocr?.keywords &&
    taskItem?.value?.external_json?.ocr?.keywords?.length > 0
  );
});
const hasKeywords = computed(() => {
  return (
    taskItem.value?.external_json?.keywords &&
    taskItem?.value?.external_json?.keywords?.length > 0
  );
});

// 计算文档类别
const docCategory = computed(() => {
  let type = taskItem.value?.doc_type || parseInt(ai_label_desc.value);
  if( type === 12 ) {
    type = 10
  }
  const forbidTypes = forbidStore.enabledForbidTags;
  const forbidType = forbidTypes?.find((item: any) => item.type === type);
  // 这里可以根据实际逻辑计算文档类别
  return forbidType?.name || '未知';
});
// 计算是否有图片识别结果
const hasImageResults = computed(() => {
  return imgInfos.value.length > 0;
});

// 计算是否显示"显示全部"按钮
const shouldShowViewAllButton = computed(() => {
  return processedImgInfos.value.length > MAX_DISPLAY_IMAGES;
});

// 显示的图片列表
const displayedImages = computed(() => {
  if (shouldShowViewAllButton.value) {
    return processedImgInfos.value.slice(0, MAX_DISPLAY_IMAGES);
  }
  return processedImgInfos.value;
});

/**
 * 打开查看全部图片的Modal
 */
const openAllImagesModal = () => {
  showAllImagesModal.value = true;
};

/**
 * 处理标签字符串，将其转换为对应的中文标签数组
 * @param {string} label - 原始标签字符串，以逗号分隔
 * @returns {string[]} - 返回处理后的中文标签数组（已去重和过滤空值），如果没有标签则返回空数组
 * @example
 * // 返回 ["涉政", "色情"]
 * processLabel("polity,porn")
 * // 返回 ["涉政"] (去重)
 * processLabel("polity,polity")
 * // 返回 [] (无标签)
 * processLabel("")
 */
const processLabel = (label:string):string[] =>{
  console.log("labels",label)
  if(!label){
    return [];
  }
  
  // 将标签字符串分割成数组
  let labels = label.split(",");
  
  // 过滤空字符串、"pass"和"ignore"标签
  labels = labels.filter(item => {
    const trimmedItem = item.trim();
    return trimmedItem !== "" && 
           trimmedItem !== "pass" && 
           trimmedItem !== "ignore";
  });
  
  // 如果没有标签，返回空数组
  if(labels.length === 0){
    return [];
  }
  
  // 将每个标签映射为对应的中文标签
  const result = labels.map(item => MACHINE_IMG_LABEL_MAP[item] || "未知");
  
  // 对结果数组进行去重
  const uniqueResult = [...new Set(result)];
  
  console.log("labels", labels, uniqueResult);
  return uniqueResult;
}
watch(
  () => taskItem.value,
  (newVal) => {
    // 检查newVal是否存在且不是空对象
    if(!!newVal && Object.keys(newVal).length > 0) {
      getPreviewImageInfoData();
    }
  },
);
</script>
<template>
  <div>
    <a-card size="small">
      <template #title>
        <div class="flex items-center justify-between" size="small">
          <span class="font-semibold">机器检测结果</span>
        </div>
      </template>
      <div class="max-h-[200px] space-y-0.5 overflow-y-auto">
        <!-- 文档类别 -->
        <Field label="文档类别" :value="docCategory" />

        <!-- 文本识别结果 -->
        <Field :direction="hasKeywords || hasOcrKeywords ? 'vertical' : 'horizontal'" label="文本识别结果" label-item-position="start">
          <word-level-box
            v-if="hasKeywords"
            :wordlist="taskItem?.external_json.keywords || []"
          ></word-level-box>
          <word-level-box
            v-if="hasOcrKeywords"
            :is-ocr="true"
            :wordlist="taskItem?.external_json.ocr.keywords || []"
          ></word-level-box>
          <span v-if="!hasKeywords && !hasOcrKeywords">无</span>
        </Field>

        <!-- 图片识别结果 -->
        <Field class="relative" :direction="hasImageResults ? 'vertical' : 'horizontal'" label="图片识别结果">
        <!-- 显示全部按钮 -->
            <a-button 
              v-if="shouldShowViewAllButton"
              type="link" 
              size="small" 
              class="absolute top-0 right-2 z-10"
              @click="openAllImagesModal"
            >
              展示全部图片
            </a-button>
          <div
            class="flex max-h-24 flex-wrap gap-2 overflow-auto border border-gray-300 p-2 overflow-y-auto relative"
            v-if="hasImageResults"
          >
            

            <img-view
              v-for="imgInfo in displayedImages"
              :key="imgInfo.url"
              :src="imgInfo.url"
              :file-item="taskItem"
              :image-item="imgInfo"
              :show-hover-image="true"
              :tag-text="imgInfo.processedLabels"
              :tag-color="imgInfo.hasLabels ? 'red' : ''"
              :tooltip-width="300"
              :tooltip-height="300"
              alt="图片识别结果"
              :class="[
                'h-20 w-20 rounded overflow-hidden',
                imgInfo.borderColor === 'red' ? 'border-red-500 border-2' : '',
                imgInfo.borderColor === 'blue' ? 'border-blue-500 border-2' : '',
                imgInfo.borderColor === 'yellow' ? 'border-yellow-500 border-2' : ''
              ]"
            />
          </div>
          <span v-else>无</span>
        </Field>
      </div>
    </a-card>

    <!-- 显示全部图片的Modal -->
    <a-modal
      v-model:open="showAllImagesModal"
      title="全部图片"
      width="80%"
      :footer="null"
      :destroyOnClose="true"
    >
      <div class="flex flex-wrap gap-2 overflow-auto p-2 max-h-[70vh]">
        <img-view
          v-for="imgInfo in processedImgInfos"
          :key="imgInfo.url"
          :src="imgInfo.url"
          alt="图片识别结果"
          :class="[
            'h-24 w-24 rounded overflow-hidden',
            imgInfo.borderColor === 'red' ? 'border-red-500 border-2' : '',
            imgInfo.borderColor === 'blue' ? 'border-blue-500 border-2' : '',
            imgInfo.borderColor === 'yellow' ? 'border-yellow-500 border-2' : ''
          ]"
        />
      </div>
    </a-modal>
  </div>
</template>
<style scoped>
:deep(.ant-card-head) {
  background-color: #006be6;
  color: #fff;
  min-height: 30px;
}
</style>
