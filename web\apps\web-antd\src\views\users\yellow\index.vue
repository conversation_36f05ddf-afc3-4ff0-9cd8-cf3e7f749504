<script setup lang="ts">
import { ref, reactive, computed, onMounted, h, watch, nextTick } from 'vue';
import Roster from '../components/Roster.vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getItem } from '#/utils/save';
import {
  formatTimestampToDate,
  disabledDate,
  disabledDateTime,
  getCurrentTimestamp,
} from '#/utils/time';
import { z } from '#/adapter/form';
import {
  getYellowList,
  isCompanyUser,
  addYellow,
  getYellowHistory,
  deleteYellow,
  updateYellow,
} from '#/api/yellow-list';
import { useUserStore } from '#/store/user';
import StandardForm from '#/components/form/index.vue';
import { Button } from 'ant-design-vue';
import { deepClone } from '#/utils';
import { useApiHandler } from '#/composables/common/use-api-handler';
import type { FormSchema } from '#/types/form';
import MfaModal from '#/components/MfaModal.vue';

const { handleApiCall } = useApiHandler();

const userStore = useUserStore();
const rosterTable = ref<any>(null);
const formKey = ref(0);
const modalForm = ref<any>(null);
const open = ref(false);
const modalType = ref('');
const platform = userStore.platform;
const initialForm: Record<string, any> = {
  userid: '',
  enable: 1,
  mark: '',
  platform: '',
  expire_time: '',
  utype: 'y',
  kind: 'userid',
};
const ruleForm = ref({ ...initialForm });
const userQuery = reactive({
  loading: false,
  disable: true,
  type: '',
});
const mfaOpen = ref(false);
const enableOptions = [
  {
    label: '全部',
    value: '',
  },
  {
    label: '开启',
    value: '0',
  },
  {
    label: '封禁',
    value: '1',
  },
];
const formInline = reactive({
  userid: '',
  platform: '',
  enable: '',
});
const searchOptions = {
  collapsed: false,
  schemas: {
    '1': [
      {
        component: 'Input',
        fieldName: 'userid',
        label: 'ID',
      },
      {
        component: 'Select',
        componentProps: {
          options: enableOptions,
          clearable: true,
        },
        fieldName: 'enable',
        label: '账号状态',
      },
    ],
  },
};
const columns: VxeGridProps['columns'] = [
  {
    type: 'seq',
    title: '序号',
    width: 60,
    align: 'center',
  },
  {
    field: 'userid',
    title: 'UserID',
    align: 'center',
  },
  {
    field: 'enable',
    title: '状态',
    align: 'center',
    slots: { default: 'enable' },
  },
  {
    field: 'effect_time',
    title: '生效时间',
    align: 'center',
    formatter: ({ cellValue }) => formatTimestampToDate(cellValue),
  },
  {
    field: 'expire_time',
    title: '过期时间',
    align: 'center',
    formatter: ({ cellValue }) => formatTimestampToDate(cellValue),
  },
  {
    field: 'mark',
    title: '备注',
    align: 'center',
  },
  {
    title: '操作',
    align: 'center',
    slots: { default: 'operation' },
  },
];

const schema = computed(() => {
  const baseFields: FormSchema[] = [
    {
      fieldName: 'userid',
      label: getUserIdLabel(),
      component: 'Input',
      defaultValue: ruleForm.value.userid,
      componentProps: {
        disabled: modalType.value !== 'add',
        allowClear: true,
        style:"width: 180px;"
      },
      suffix: () => (modalType.value === 'add' ? suffix.value : ''),
      rules: 'required',
    },
  ];

  if (['add', 'ban', 'update'].includes(modalType.value)) {
    baseFields.push({
      fieldName: 'expire_time',
      label: '失效时间',
      component: 'DatePicker',
      defaultValue: ruleForm.value.expire_time,
      componentProps: {
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'x',
        placeholder: '选择失效时间',
        class: 'w-full',
        showTime: true,
        disabledDate: disabledDate,
        disabledTime: disabledDateTime,
      },
      help: '不选择失效时间则代表永久封禁',
      rules: z.string().optional()
        .refine((value) => {
          // 如果没有值，表示永久封禁，直接通过验证
          if (!value) return true;
          // 将时间戳转换为毫秒（如果是秒级时间戳）
          const timestamp = String(value).length === 10 ? Number(value) * 1000 : Number(value);
          const currentTime = Date.now();
          // 验证时间是否大于当前时间
          return timestamp > currentTime;
        }, { message: '失效时间必须大于当前时间' }),
    });
  }

  if (modalType.value === 'ban') {
    baseFields.push({
      fieldName: 'last_ban',
      component: 'Input',
      labelWidth: 0
    });
  }

  baseFields.push({
    fieldName: 'mark',
    label: getMarkLabel(),
    component: 'Textarea',
    defaultValue: ruleForm.value.mark,
    rules: 'required',
  });

  return baseFields;
});

const getUserIdLabel = () => {
  return modalType.value === 'add' ? 'ID' : '用户ID';
};

const getMarkLabel = () => {
  const labels: Record<string, string> = {
    add: '备注',
    ban: '封禁原因',
    update: '修改原因',
    unseal: '解禁原因',
  };
  return labels[modalType.value] || '备注';
};

const commonConfig = {
  labelWidth: 90,
};
const wrapperClass = 'pr-6';
const lastTime = reactive({
  startTime: '',
  endTime: '',
});

const mfaTitle = ref('该操作实时生效，请谨慎操作！');
const mfaShow = ref(false);
const mfaType = ref('');
const curDelData = ref();

// const isAddType = computed(() => modalType.value === 'add');
// const isBanType = computed(() => modalType.value === 'ban');
// const isUnsealType = computed(() => modalType.value === 'unseal');
const isUpdateType = computed(() => modalType.value === 'update');
const userType = computed(() => {
  if (typeof userQuery.type === 'boolean') {
    return userQuery.type ? '企业用户' : '非企业用户';
  }
  return userQuery.type;
});

const modalTitle = computed(() => {
  if (modalType.value === 'add') return '账号操作-用户禁分享';
  else if (modalType.value === 'ban') {
    return '账号操作-用户禁分享-封禁';
  } else if (modalType.value === 'unseal') {
    return '账号操作-用户禁分享-解禁';
  } else if (modalType.value === 'update') {
    return '账号操作-用户禁分享-修改';
  }
});
const suffix = computed(() => [
  h(
    Button,
    {
      disabled: userQuery.disable,
      onClick: checkCompanyUser,
      loading: userQuery.loading,
      type: 'link',
    },
    () => (userQuery.loading ? '正在查询' : '查询身份'),
  ),
  h('span', { class: 'text-red-500 whitespace-normal' }, userType.value),
]);

async function getList({
  page,
  pageSize,
  ...formValues
}: {
  page: number;
  pageSize: number;
  [key: string]: any;
}) {
  let params = {
    // user_id: userStore.userid,
    page: page - 1,
    limit: pageSize,
    platform
  };
  if (formValues) {
    Object.assign(params, formValues);
  }
  const { data } = await getYellowList(params);
  return {
    items: data.data,
    total: data.count,
  };
}

function handleCreate() {
  modalType.value = 'add';
  open.value = true;

  ruleForm.value = { ...initialForm };
}
async function handleBan(row: any) {
  modalType.value = 'ban';
  ruleForm.value = deepClone(row);

  open.value = true;
  ruleForm.value.enable = 1;
  ruleForm.value.mark = '';
  ruleForm.value.expire_time = '';
  await modalForm.value?.formApi.setValues({ ...ruleForm.value });
  console.log('封禁', row, ruleForm.value);
  const { data,result } = await getYellowHistory({
    user_id: userStore.userid,
    userid: row.userid,
  });
  console.log("data",data);
  if (data && result) {
    let actionList = data || [];
    actionList.sort((before: any, after: any) => {
      if (before.start_time < after.start_time) return -1;
      else if (before.start_time > after.start_time) return 1;
      return 0;
    });
    const actionLen = actionList.length;
    let lastUpdateAction = null;
    for (let i = actionLen - 1; i >= 0; i--) {
      const action = actionList[i];
      // 最近一次操作为constraint_delete,说明在手动解禁
      if (i === actionLen - 1) {
        lastTime.endTime =
          action.operation === 'constraint_delete'
            ? action.start_time
            : row.expire_time;
      }
      // constraint_delete使得查找constraint_update中断
      if (action.operation === 'constraint_delete' && !!lastUpdateAction) break;
      if (action.operation === 'constraint_update') {
        if (
          !lastUpdateAction ||
          (lastUpdateAction.start_time > action.start_time &&
            (lastUpdateAction.start_time < action.end_time ||
              action.end_time === -1))
        ) {
          lastTime.startTime = action.start_time;
        }
        lastUpdateAction = action;
      }
    }
    if (!lastTime.startTime) lastTime.startTime = row.effect_time;
    if (!lastTime.endTime) lastTime.endTime = row.expire_time;
  }
}
async function handleUnseal(row: any) {
  modalType.value = 'unseal';
  ruleForm.value = deepClone(row);

  await modalForm.value?.formApi.setValues({ ...ruleForm.value });
  open.value = true;

  ruleForm.value.enable = 0;
  ruleForm.value.mark = '';
  ruleForm.value.expire_time = '';
}
async function handleEdit(row: any) {
  modalType.value = 'update';
  ruleForm.value = deepClone(row);
  console.log('修改', row, ruleForm.value);
  open.value = true;

  ruleForm.value.expire_time =
    row.expire_time === -1 ? '' : row.expire_time * 1000;
  ruleForm.value.mark = '';
  // await nextTick();
  await modalForm.value?.formApi.setValues({ ...ruleForm.value });
}

function handleDelete(row: any) {
  mfaShow.value = true;
  mfaType.value = 'del';
  curDelData.value = row;
}
async function deleteConfirmCommit() {
  await handleApiCall(
    deleteYellow,
    {
      ...curDelData.value,
      effect_time: Math.floor(Date.now() / 1000),
      expire_time: -1,
    },
    {
      successMsg: '删除成功',
      onSuccess: () => rosterTable.value?.refreshTable(),
    },
  );
}
async function onSubmit() {
  mfaShow.value = false;
  let params = deepClone(ruleForm.value);
  params.platform = platform;
  params.expire_time = Math.floor(params.expire_time / 1000);
  params.userid = Number(params.userid);
  const data = {
    user_id: params.user_id,
    index_id: String(params.userid),
    userid: params.userid,
    enable: params.enable,
    mark: params.mark,
    platform: params.platform,
    // expire_time为空时表示永久封禁,传入-1
    expire_time: params.expire_time || -1,
    effect_time: isUpdateType
      ? params.effect_time
      : Math.floor(Date.now() / 1000),
    utype: params.utype,
    kind: params.kind,
  };

  const options = {
    successMsg: '保存成功',
    onSuccess: () => {
      rosterTable.value?.refreshTable();
    },
  };
  if (modalType.value === 'add') {
    await handleApiCall(addYellow, data, options);
  } else {
    await handleApiCall(updateYellow, data, options);
  }
}
async function handleMfaOk() {
  if (mfaType.value === 'del') await deleteConfirmCommit();
  else if (mfaType.value === 'cr') await onSubmit();
  mfaShow.value = false;
  open.value = false;
  rosterTable.value?.refreshTable();
}
function handleModalValuesChange(values: any) {
  ruleForm.value = { ...ruleForm.value, ...values };
  console.log('form change', values, ruleForm.value);
  if(ruleForm.value.userid) {
    console.log("sss");

    userQuery.disable = false;
    console.log('userQuery.disable 已设置为 false'); // 验证是否触发
  }
  // console.log('modal变化ruleForm', ruleForm.value);
}
async function checkCompanyUser() {
  userQuery.loading = true;
  let params = {
    user_id: userStore.userid,
    userid: ruleForm.value.userid,
  };
  const data = await isCompanyUser(params);
  userQuery.loading = false;
  userQuery.type = data.result;
}
async function checkMFA() {
  const res = await modalForm.value?.formApi.validate();

  if (res.valid) {
    mfaType.value = 'cr';
    mfaShow.value = true;
  }
}
function resetState() {
  userQuery.loading = false;
  userQuery.disable = true;
  userQuery.type = '';
  modalType.value = '';
  lastTime.startTime = '';
  lastTime.endTime = '';
}

onMounted(() => {
  formInline.platform = platform;
});
watch(open, async (newVal) => {
  if (!newVal) {
    await modalForm.value?.formApi.resetForm();
    resetState();
  }
});
watch(modalType, (newVal) => {
  console.log('modalType变化', newVal);
  formKey.value++; // 通过 key 变化强制重新渲染
});
</script>
<template>
  <div>
    <roster
      ref="rosterTable"
      roster-type="yellow"
      :search-options="searchOptions"
      :columns="columns"
      :query-method="getList"
      @on-create="handleCreate"
      @on-ban="handleBan"
      @on-lift="handleUnseal"
      @on-edit="handleEdit"
      @on-delete="handleDelete"
    >
    </roster>
    <a-modal v-model:open="open" :title="modalTitle" @ok="checkMFA">
      <standard-form
        :key="formKey"
        ref="modalForm"
        :schema="schema"
        :showDefaultActions="false"
        :common-config="commonConfig"
        :wrapper-class="wrapperClass"
        :handle-values-change="handleModalValuesChange"
      >
        <template #last_ban>
          <a-alert
            :message="`上一次封禁时间周期：${formatTimestampToDate(Number(lastTime.startTime) * 1000)} —— ${formatTimestampToDate(Number(lastTime.endTime) * 1000)}`"
          type="error"
          />
        </template>
      </standard-form>
    </a-modal>
    <!-- 使用封装的 MFA 验证弹窗组件 -->
    <mfa-modal
      v-model:visible="mfaShow"
      :title="mfaTitle"
      @confirm="handleMfaOk"
    />
  </div>
</template>
