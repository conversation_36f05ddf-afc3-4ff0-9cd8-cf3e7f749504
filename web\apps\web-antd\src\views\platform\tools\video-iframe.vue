<script lang="ts" setup>
import { ref } from "vue";
import { getVideoIframe } from "#/api/tools";
import { useUserStore } from "#/store";
import { message } from "ant-design-vue";
import {
  UploadOutlined,
  CloseCircleOutlined,
  QuestionCircleOutlined,
} from "@ant-design/icons-vue";
import { FRAME_TYPE, FRAME_SPAN_TYPE } from "#/constants/maps/tools";
import { mapToOptions } from "#/utils/transform";
const userStore = useUserStore();
const userid = userStore.userid;

const isUploading = ref(false);
const isSpliting = ref(false);
const iframsRes = ref<string[]>([]);
const videoSrc = ref("");
const frameType = ref(2);
const interval = ref(10);
const quality = ref(1);

const uploadCheck = (file: any) => {
  isUploading.value = true;
  const ext = file.name.split(".").pop()?.toLowerCase();

  if (["mp4"].indexOf(ext as string) > -1) {
    return true;
  }
  message.error("文件格式不支持，请重新选择文件");
  isUploading.value = false;
  return false;
};

const uploadStatus = (resp: any, files: any) => {
  if (!resp || !resp.result || !resp.data) {
    message.error("上传失败，请联系管理员");
    return;
  }
  isUploading.value = false;
  videoSrc.value = resp.data.urls[0];
};

const beginWork = async () => {
  if (!videoSrc.value) {
    message.error("请先上传视频");
    return;
  }
  const params = {
    video_type: "ks3",
    video_src: videoSrc.value,
    mime_type: "video/mp4",
    frame_type: Number(frameType.value),
    interval: interval.value,
    quality: quality.value,
  };
  isSpliting.value = true;
  const res = await getVideoIframe(params);
  isSpliting.value = false;
  iframsRes.value = res.data;
};

const removeItem = (index: number) => {
  iframsRes.value.splice(index, 1);
};

const imgDownload = () => {
  iframsRes.value.forEach((link) => {
    window.open(link);
  });
};
</script>

<template>
  <div class="p-[20px] text-sm">
    <a-row>
      <a-col :span="8">
        <div class="p-5 text-sm">
          <a-button type="primary" :loading="isUploading" v-if="isUploading"
            >视频上传中</a-button
          >
          <a-upload
            v-show="!isUploading"
            class="inline-block"
            :showUploadList="false"
            :action="'/manual/common/file/upload?user_id=' + userid"
            :beforeUpload="uploadCheck"
            :on-success="uploadStatus"
          >
            <a-button size="middle" type="primary">
              <template #icon><UploadOutlined /></template>
              视频上传
            </a-button>
          </a-upload>
          <div class="my-[10px]">
            <span>视频地址：{{ videoSrc }}</span>
          </div>
          <div class="my-[10px]">
            <span>切帧类型：</span>
            <a-select
              v-model:value="frameType"
              placeholder="请选择"
              class="mb-[5px] ml-[0px] mr-[15px] mt-[0px] w-[130px]"
              :options="mapToOptions(FRAME_TYPE)"
            >
            </a-select>
          </div>
          <div class="my-[10px] flex items-center">
            <span>{{ FRAME_SPAN_TYPE[frameType] + "：" }}</span>
            <a-input-number
              class="mr-[5px]"
              v-model:value="interval"
              :min="0"
            ></a-input-number>
            <span v-if="frameType === 2">s</span>
          </div>
          <div class="my-[10px] flex items-center">
            <span>切帧图片质量：</span>
            <a-tooltip class="mr-[5px]">
              <template #title>
                <span>生效范围1-51，1最高（最清晰），51最低</span>
              </template>
              <QuestionCircleOutlined />
            </a-tooltip>
            <a-input-number
              defaultValue="1"
              v-model="quality"
              :min="1"
              :max="51"
            ></a-input-number>
          </div>
          <div class="my-[10px]">
            <a-button
              type="primary"
              :disabled="isUploading"
              :loading="isSpliting"
              v-if="isSpliting"
            >
              <template #icon><CloseCircleOutlined /></template>
              抽帧中
            </a-button>
            <a-button @click="beginWork" type="primary" :disabled="isUploading" v-else>
              <template #icon><CloseCircleOutlined /></template>
              开始抽帧
            </a-button>
          </div>
        </div>
      </a-col>

      <a-col :span="16">
        <a-card class="min-h-[500px]">
          <div slot="header" class="clearfix">
            <span>抽帧结果</span>
          </div>
          <div class="clearfix">
            <div
              class="relative float-left m-[8px] h-[200px] w-[200px]"
              v-for="(item, index) in iframsRes"
              :key="index"
            >
              <div
                class="absolute right-[-12px] top-[-20px] z-[100] cursor-pointer text-[28px]"
                @click="removeItem(index)"
              >
                <CloseCircleOutlined />
              </div>
              <a-image
                :height="200"
                :width="200"
                :src="item"
                :preview-src-list="iframsRes"
              />
            </div>
          </div>
          <a-button @click="imgDownload" type="primary" class="mt-[10px]">
            图片下载
          </a-button>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>
