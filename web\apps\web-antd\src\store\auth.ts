import type { Recordable, SetUserInfoPayload } from '@vben/types';

import { ref } from 'vue';
import { useRouter } from 'vue-router';

import { DEFAULT_HOME_PATH, LOGIN_PATH } from '@vben/constants';
import { resetAllStores, useAccessStore, } from '@vben/stores';
import { error,warning } from '#/utils/toast';
import { useUserStore, usePageStore, useBadgeStore } from '../store';
import { defineStore } from 'pinia';
import { getAllCount } from '#/api/task'

import { getUserInfoApi, getUserPermissionApi, loginApi } from '#/api';
import { getEntranceList } from '#/api/role';

import { setItem } from '#/utils/save';

export const useAuthStore = defineStore('auth', () => {
  const pageStore = usePageStore();
  const accessStore = useAccessStore();
  const badgeStore = useBadgeStore();

  // const userStore = useUserStore();
  const router = useRouter();
  const myUserStore = useUserStore();
  // myUserStore.setUserInfo('a')

  const loginLoading = ref(false);

  /**
   * 异步处理登录操作
   * Asynchronously handle the login process
   * @param params 登录表单数据
   * @param onSuccess 登录成功回调
   */
  async function authLogin(
    params: Recordable<any>,
    onSuccess?: (res: any) => Promise<void> | void,
  ) {
    try {
      loginLoading.value = true;
      const response = await loginApi(params);
      // 只有result true 才能得到这个 response【因为做了统一响应拦截】 那我怎么判断呢
      console.log("登录返回结果response", response);
      const data = response.data;
      console.log("登录返回结果",data);
      if(response.result){
        // 登录成功 存储user_id
        setItem("user_id",data.user_id);
        await onSuccess?.(data); // 调用成功回调，显示滑块验证
      }else{
        error(response.message);
      }
    } catch (error) {
      // result 为false 进入这里 status_code为2不知道result为多少，暂未添加逻辑
      console.log("authLogin登录失败", error);
    } finally {
      loginLoading.value = false;
    }
  }

  async function completeLogin() {
    // 并行请求，减少等待时间
    const [userPermissionRes, entranceRes, countRes] = await Promise.all([
      getUserPermissionApi(),
      getEntranceList({ type: 1, limit: -1, path: '', is_exact: false }),
      getAllCount()
    ]);

    // 解构出需要的数据
    const permisionData = userPermissionRes.data.data;
    const allEntranceData = entranceRes.data.data;
    const countData = countRes.data;
    if(!permisionData|| permisionData.length === 0){
      warning("该用户未配置任何菜单！");
      return;
    }
    // 将 permissionData 转换为查找表
    const permissionMap = new Map();
    permisionData.forEach((perm: any) => {
      const platforms = perm.platform.split(','); // 将 platform 拆分为数组
      permissionMap.set(perm.path, platforms);
    });
    // 过滤 allEntranceData
    const entranceFilterPermissionData = allEntranceData
      .filter((item: any) => permissionMap.has(item.path)) // 过滤出 permissionData 中存在的 path
      .map((item: any) => {
        const allowedPlatforms = permissionMap.get(item.path); // 获取允许的 platforms
        const filteredBizConfigArr = item.biz_config_arr?.filter((bizConfig: any) =>
          allowedPlatforms.includes(bizConfig.biz))
        return {
          ...item,
          biz_config_arr: filteredBizConfigArr, // 只保留允许的 biz_config_arr
        };
      });
 
    pageStore.setPagePermissions(
      permisionData
    );
    badgeStore.setEntranceFilterPermissionData(entranceFilterPermissionData)
    badgeStore.setBadge(countData)
    
    accessStore.setAccessToken('123456');
    router.push(DEFAULT_HOME_PATH);
  }

  async function fetchUserInfo(user_id: string) {
    let userInfo: null | SetUserInfoPayload = null;
    userInfo = await getUserInfoApi({ act_id: user_id });
    myUserStore.setUserInfo(userInfo?.data.data);
    return userInfo;
  }
  async function logout() {
    try {
      await myUserStore.logOut()
      setTimeout(() => {
        resetAllStores();
        // location.reload(); 会导致路由残存的问题
        router.push(LOGIN_PATH);
      }, 300);
    } catch (error) {
      console.log("退出登录失败", error);
    }
  }

  function $reset() {
    loginLoading.value = false;
  }

  return {
    $reset,
    authLogin,
    completeLogin,
    fetchUserInfo,
    loginLoading,
    logout,
  };
});
