<script lang="ts" setup>
import { ref, computed, type Component, shallowRef } from 'vue';
import TableTemplate from "#/components/table/index.vue";
import AiMarkSelect from "#/components/select/ai-mark.vue";
import { AI_ENTRY_MAP, INTERCEPT_REASON_MAP, LAST_STATUS_MAP, IO_EXTRAID_MAP } from '#/constants/maps/ai-mark'
import { queryCustmizeLabel, aiRecordList } from '#/api/ai-mark'
import { getCurrentMomentRange, transformTimeFields } from '#/utils/time';
import { formatTimestampToDate } from '#/utils/time';
import { mapToOptions } from '#/utils/transform';
import type { VxeGridProps } from '@vben/plugins/vxe-table';
import UpdateAimarkBtn from '#/components/UpdateAimarkBtn.vue';
// 定义 FormSchema 接口以确保类型安全
interface FormSchema<T extends string = string> {
  component: string | Component; // 组件名称，如 'Input', 'Button', 'RangePicker' 等
  fieldName: T; // 表单字段名称，用于绑定数据模型
  label?: string; // 表单字段的标签文本
  componentProps?: Record<string, any>; // 组件的属性配置
  defaultValue?: any; // 默认值
  class?: string;
}

const docTable: any = ref(null);
// 分页配置
const paginationOptions = {
  pageSize: 20,
  currentPage: 1
};
// 获取筛选数据
const getOptData = async (values: Record<string, any>) => {
  const entryValue = values.entry;  // 获取入口选择框的值
  let params: Record<string, any> = { enable: 1 };  // 默认传递 enable: 1

  if (entryValue) {
    params.entry = entryValue; // 映射入口值
  }

  try {
    const response = await queryCustmizeLabel(params);
    return response.data;
  } catch (error) {
    console.error('获取筛选数据失败:', error);
  }
};

// 获取数据列表
const getData = async ({
  page,
  pageSize,
  ...formValues
}: any) => {
  // 直接解构赋值请求参数
  const params = {
    page: page - 1,
    limit: pageSize,
    ...formValues
  };

  try {
    const response = await aiRecordList(params);
    return {
      items: response.data.records || [],
      total: response.data.total || response.data.records?.length || 0
    };
  } catch (error) {
    return { items: [], total: 0 };
  }
};

const aimarkLabelMap = ref<{ [key: string]: string }>({});
const aimarkLabelOptions = ref<any[]>([]);

// 处理搜索的方法
const handleSearch = async ({ page, pageSize, ...values }: { page: number; pageSize: number } & Record<string, any>) => {
  const labelData = await getOptData(values);
  if (labelData && labelData.length > 0) {
    aimarkLabelOptions.value = labelData.map((item: any) => ({
      label: item.title || item.label,
      value: item.type || item.value
    }));
  }

  // 使用 transformTimeFields 转换时间字段
  const transformedValues = transformTimeFields(values, [["dateRange", ["start_time", "end_time"]]]);

  return await getData({ page: page, pageSize: pageSize, ...transformedValues });
};

const getAimarkLabel = (val: any[]) => {
  aimarkLabelOptions.value = val;
  aimarkLabelMap.value = val.reduce((obj, item) => {
    obj[item.value] = item.label;
    return obj;
  }, {});
};


const rangePickerConfig: FormSchema = {
  component: 'RangePicker', // 使用 RangePicker 组件
  fieldName: 'dateRange', // 字段名称为 'dateRange'
  label: '时间范围', // 标签文本为 '时间范围'
  class: 'col-span-2',
  defaultValue: getCurrentMomentRange('X'), // 默认值为当前时刻（包含时分秒）
  componentProps: {
    placeholder: ['开始时间', '结束时间'], // 占位符文本
    format: 'YYYY-MM-DD HH:mm:ss', // 日期格式
    valueFormat: 'X',
    showTime: true, // 显示时间选择器
  },
};

// 生成输入框配置的函数
const inputConfig = (label: string, placeholder: string, fieldName: string): FormSchema => ({
  component: 'Input', // 使用 Input 组件
  fieldName, // 使用传入的字段名称
  label, // 标签文本
  componentProps: { placeholder }, // 占位符文本
});

// 生成选择框配置的函数
const selectConfig = (label: string, placeholder: string, options: any[], fieldName: string): FormSchema => ({
  component: 'Select', // 使用 Select 组件
  fieldName, // 使用传入的字段名称
  label, // 标签文本
  componentProps: {
    placeholder, // 占位符文本
    options, // 选项列表
  },
});

const reviewTypeConfig: FormSchema = {
  component: shallowRef(AiMarkSelect), // 使用 AiMarkSelect 组件
  fieldName: 'type', // 字段名称为 'type'
  label: '复审类型', // 标签文本为 '复审类型'
  componentProps: {
    placeholder: '请选择复审类型', // 占位符文本
    onAimarkLabel: getAimarkLabel
  },
};

// 入口选择框配置
const entryConfig: FormSchema = {
  component: 'Select', // 使用 Select 组件
  fieldName: 'entry', // 字段名称为 'entry'
  label: '入口', // 标签文本为 '入口'
  componentProps: {
    placeholder: '请选择入口',
    options: mapToOptions(AI_ENTRY_MAP),
  },
};

// 拦截原因选择框配置
const interceptReasonConfig: FormSchema = {
  component: 'Select',
  fieldName: 'reasons[]',
  label: '拦截原因',
  componentProps: {
    placeholder: '请选择拦截原因',
    options: mapToOptions(INTERCEPT_REASON_MAP),
  },
};

// 机审结果选择框配置
const machineResultConfig: FormSchema = {
  component: 'Select',
  fieldName: 'last_status',
  label: '机审结果',
  componentProps: {
    placeholder: '请选择机审结果',
    options: mapToOptions(LAST_STATUS_MAP),
  },
};

// ExtraID 选择框配置
const extraIdConfig: FormSchema = {
  component: 'Select',
  fieldName: 'extra_id',
  label: 'ExtraID',
  componentProps: {
    placeholder: '请选择 ExtraID',
    options: mapToOptions(IO_EXTRAID_MAP),
  },
};

// 表单配置
const schema = computed(() => [
  inputConfig('操作人', '请输入操作人', 'reviewer_name'), // 操作人输入框，字段名为 'reviewer_name'
  inputConfig('文本搜索', '请输入搜索内容', 'text'), // 文本搜索输入框，字段名为 'text'
  inputConfig('msg_id', '请输入msg_id', 'msg_id'), // msg_id 输入框，字段名为 'msg_id'
  reviewTypeConfig, // 复审类型选择框
  selectConfig('状态', '请选择状态', [
    { label: '全部', value: '' },
    { label: '白样本', value: 'ok' },
    { label: '黑样本', value: 'forbid' },
  ], 'status'), // 状态选择框，字段名为 'status'
  entryConfig, // 入口选择框，字段名为 'entry'
  interceptReasonConfig, // 拦截原因选择框，字段名为 'reasons[]'
  machineResultConfig, // 机审结果选择框，字段名为 'last_status'
  extraIdConfig, // ExtraID 选择框，字段名为 'extra_id'
  rangePickerConfig, // 时间范围选择器
]);

const columns: VxeGridProps["columns"] = [
  {
    type: "seq", align: "center",
    title: "序号",
    fixed: "left",
    width: 100,
  },
  {
    title: '状态',
    field: 'status',
  },
  {
    title: 'msg_id',
    field: 'fileid',
  },
  {
    title: '拦截原因',
    field: 'reasons',
    slots: { default: 'intercept-reasons' }
  },
  {
    title: '机审结果',
    field: 'last_status',
    formatter: ({ cellValue }) => LAST_STATUS_MAP[cellValue] || cellValue
  },
  {
    title: 'ExtraID',
    field: 'extra_id',
  },
  {
    title: '内容',
    field: 'content',
  },
  {
    title: '操作人',
    field: 'reviewer_name',
  },
  {
    title: '复审类型',
    field: 'type',
    formatter: ({ cellValue }) => aimarkLabelMap.value[cellValue] || cellValue
  },
  {
    title: '入口',
    field: 'entry',
    formatter: ({ cellValue }) => AI_ENTRY_MAP[cellValue] || cellValue // 映射入口
  },
  {
    title: '操作时间',
    field: 'modify_time',
    formatter: ({ cellValue }) => formatTimestampToDate(cellValue)
  },
  {
    title: '关联审核信息',
    field: 'related_msg',
  },
  {
    title: '操作',
    field: 'operation',
    slots: { default: 'operation' }
  },
];


// 动态生成 searchOptions
const searchOptions = computed(() => ({
  collapsed: false,
  schemas: {
    '1': schema.value,
  },
  showCollapseButton: false,
  submitOnChange: false,
  wrapperClass: 'grid-cols-8',
}));
function refresh() {
  docTable.value?.refreshTable();
}
</script>

<template>
  <table-template :columns="columns" :pagination-options="paginationOptions" :query-method="handleSearch"
    :search-options="searchOptions" ref="docTable">
    <template #intercept-reasons="{ row }">
      <span v-if="!row.reasons || row.reasons.length === 0">--</span>
      <span v-else>
        {{row.reasons.map((key: string | number) => INTERCEPT_REASON_MAP[key] || key).join(', ')}}
      </span>
    </template>
    <template #operation="{ row }">
      <update-aimark-btn
        :aimark-label-options="aimarkLabelOptions"
        :scope="row"
        :call-back="refresh"
      ></update-aimark-btn>
    </template>
  </table-template>
</template>
