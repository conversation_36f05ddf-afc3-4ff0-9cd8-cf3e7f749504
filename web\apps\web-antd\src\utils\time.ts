/** 这个文件下存放跟时间处理有关的公共方法 */
import dayjs from 'dayjs';
/**
 * 将时间戳格式化为 GMT 字符串
 * @param timestamp 时间戳（秒或毫秒），可选参数
 * @param fallback 当时间戳无效时返回的默认值，默认为 '-'
 * @returns GMT 格式的日期字符串，如果时间戳无效则返回 fallback 值
 */
export function formatTimestampToGMT(
  timestamp?: number | null,
  fallback: string = '-',
) {
  if (timestamp == null || isNaN(timestamp)) {
    return fallback;
  }
  const isSecondsTimestamp = timestamp.toString().length <= 10;

  try {
    const date = isSecondsTimestamp ? dayjs.unix(timestamp) : dayjs(timestamp);
    return date.toDate().toUTCString();
  } catch (error) {
    return fallback;
  }
}

/**
 * 将时间戳转换为指定格式的日期字符串
 * @param timestamp 时间戳（秒）
 * @param format 日期格式，默认为 'YYYY-MM-DD'
 * @param fallback 错误回调展示
 * @returns 格式化后的日期字符串
 *
 * 示例:
 * ```
 * formatTimestamp(1696089600, 'YYYY-MM-DD')  // '2023-10-01'
 * formatTimestamp(1696089600, 'YYYY-MM-DD HH:mm:ss')  // '2023-10-01 00:00:00'
 * ```
 */
export function formatTimestampToDate(
  timestamp?: number | null,
  format = 'YYYY-MM-DD HH:mm:ss',
  fallback: string = '-',
) {
  if (timestamp == null || timestamp == 0 || isNaN(timestamp)) {
    return fallback;
  }
  const isSecondsTimestamp = timestamp.toString().length <= 10;

  try {
    const date = isSecondsTimestamp ? dayjs.unix(timestamp) : dayjs(timestamp); //判断时间戳是否为秒级（通常长度不超过 10 位）。
    return date.format(format);
  } catch (error) {
    return fallback;
  }
}

/**
 * 将两个时间戳格式化为指定格式的日期范围字符串
 *
 * 此函数接收两个时间戳参数，以及一个可选的日期格式字符串，默认格式为'YYYY-MM-DD'
 * 它将这两个时间戳格式化为指定格式的日期，并以'~'符号连接起来，形成一个日期范围
 *
 * @param ts1 第一个时间戳，表示日期范围的开始
 * @param ts2 第二个时间戳，表示日期范围的结束
 * @param format 日期格式字符串，默认为'YYYY-MM-DD HH:mm:ss'
 * @returns 返回格式化后的日期范围字符串，如'2023-01-01 12:00:00~2023-01-10 13:00:00'
 */
export function formatTimestampToRangeDate(
  ts1: number,
  ts2: number,
  format = 'YYYY-MM-DD HH:mm:ss',
) {
  const start = formatTimestampToDate(ts1, format);
  const end = formatTimestampToDate(ts2, format);
  // 将格式化后的日期字符串以'~'符号连接起来，形成日期范围
  return `${start}~${end}`;
}

/**
 * 获取最近N天的时间范围
 *
 * @param {number} n - 从今天往前推的天数（默认7天）
 * @param {number} m - 结束日期偏移天数（默认1天，即昨天）
 * @param {string} format - 格式化字符串（空则返回Day.js对象）
 *                         Format string (empty returns Day.js objects)
 *                         'X' - 10位Unix时间戳 | 10-digit Unix timestamp
 *                         'x' - 13位毫秒时间戳 | 13-digit millisecond timestamp
 * @returns {Array} 返回开始和结束时间的数组
 */
export function getLastNDaysRange(
  n = 7,
  m = 1,
  format = '',
  useCustomRange = false,
) {
  const today = dayjs();

  let start, end;
  if (useCustomRange) {
    end = today.endOf('day');
    start = today.subtract(n - 1, 'day').startOf('day');
  } else {
    end = today.subtract(m, 'day').endOf('day'); // m 天前 23:59:59
    start = today.subtract(n, 'day').startOf('day'); // n 天前 00:00:00
  }
  // 明确定义 formatter 的类型
  const formatter: {
    [key: string]: (d: dayjs.Dayjs) => number | string | dayjs.Dayjs;
    default: (d: dayjs.Dayjs) => dayjs.Dayjs | string;
  } = {
    X: (d: dayjs.Dayjs) => d.unix(),
    x: (d: dayjs.Dayjs) => Number(d),
    default: (d: dayjs.Dayjs) => (format ? d.format(format) : d),
  };

  const formatFn = formatter[format] || formatter.default;

  return [start, end].map(formatFn);
}

/**
 * 获取最近 N 月的时间范围
 *
 * @param n 从当前月往前推的月数（默认 1，即上个月开始）
 * @param m 结束日期偏移月数（默认 0，即本月结束；如果想结束到上个月末，传 1）
 * @param format 格式化字符串：
 *               ''    - 返回 dayjs 对象
 *               'X'   - 返回 10 位 Unix 时间戳（秒）
 *               'x'   - 返回 13 位毫秒时间戳
 *               其他   - 按 dayjs.format(format) 输出
 * @returns [start, end]，格式化后的数组
 */
export function getLastNMonthsRange(
  n: number = 1,
  m: number = 0,
  format: '' | 'X' | 'x' | string = '',
): Array<number | string | dayjs.Dayjs> {
  // 结束：当前时间减 m 月，取当月最后一刻
  const end = dayjs().subtract(m, 'month').endOf('month');
  // 开始：当前时间减 n 月，取当月第一刻
  const start = dayjs().subtract(n, 'month').startOf('month');

  const formatter: {
    [key: string]: (d: dayjs.Dayjs) => number | string | dayjs.Dayjs;
    default: (d: dayjs.Dayjs) => dayjs.Dayjs | string;
  } = {
    X: (d) => d.unix(),
    x: (d) => d.valueOf(),
    default: (d) => (format ? d.format(format) : d),
  };

  const fn = formatter[format] || formatter.default;
  return [start, end].map(fn);
}

/**
 * 获取当前月份起始时间戳
 * @param format 格式类型：
 *               'X' - 返回当月1日00:00:00的秒级时间戳
 *               'x' - 返回毫秒级时间戳
 *               'string' - 返回'YYYY-MM'格式字符串
 * @returns 根据格式返回对应类型的值
 */
export function getCurrentMonthStart(format: 'X' | 'x' | 'string' = 'X') {
  const start = dayjs().startOf('month');

  switch (format) {
    case 'X':
      return start.unix();
    case 'x':
      return start.valueOf();
    case 'string':
      return start.format('YYYY-MM');
    default:
      return start.unix();
  }
}

/**
 * 将对象中的时间字段转换为开始和结束字段
 * @param obj 原始对象，包含需要转换的时间字段
 * @param fieldMappings 字段映射配置，格式为 [原字段名, [开始字段名, 结束字段名]]
 * @returns 转换后的对象，包含新的开始和结束字段
 */
export function transformTimeFields(
  obj: Record<string, any>,
  fieldMappings: Array<[string, [string, string]]>,
): Record<string, any> {
  return fieldMappings.reduce(
    (acc, [sourceField, [startField, endField]]) => {
      if (acc.hasOwnProperty(sourceField)) {
        const [start = null, end = null] = acc[sourceField] || [];
        acc[startField] = start ? Number(start) : undefined;
        acc[endField] = end ? Number(end) : undefined;
        delete acc[sourceField];
      }
      return acc;
    },
    { ...obj },
  );
}
// 计算后 n 天的 23:59:59（10 位时间戳）
export function getEndOfDayTimestamp(startTimestamp: number, daysAfter = 1) {
  return dayjs(startTimestamp * 1000) // 转为毫秒级
    .add(daysAfter, 'day') // 加 n 天
    .endOf('day') // 设置为 23:59:59.999
    .unix(); // 返回 10 位秒级时间戳
}

// 计算当月最后一天的时间戳（10 位时间戳）
export function getEndOfMonthTimestamp(timestamp: number) {
  return dayjs(timestamp * 1000) // 转为毫秒级
    .endOf('month') // 设置到当月最后一天
    .unix(); // 返回秒级时间戳
}

/**
 * 将秒数转换为易读的时间格式
 * @param td 秒数
 * @returns 格式化后的时间字符串，如 "12秒", "1分06秒", "3时22分13秒"
 */
export function formatSecondsToReadableTime(td: number) {
  let s = '';
  let t = td > 0 ? td : 0;
  const rule: Record<string, number> = { 秒: 60, 分: 60, 时: 24, 天: -1 };
  const unit = ['秒', '分', '时', '天'] as const;
  for (const d of unit) {
    const e = rule[d] as number;
    const m = Math.floor(t / e);
    const n = Math.floor(t % e);
    if (e === -1) {
      s = `${t}${d}` + s;
      return s;
    }
    s = `${n}${d}` + s;
    t = m;
    if (t === 0) {
      return s;
    }
  }
  return s;
}
// 获取当前时间戳(秒)
export function getCurrentTimestamp() {
  return Math.round(Date.now() / 1000);
}

//  计算天数对应的秒数
export function daysToSeconds(days: number) {
  return days * 24 * 60 * 60;
}

/**
 * 将小时数字转换为时间范围字符串
 *
 * @param hour - 小时数字（0-23）
 * @returns 格式化的时间范围字符串，格式为 "HH:00~HH:59"
 *
 * @example
 * // 示例1：处理个位数小时
 * const timeRange1 = formatHourToTimeRange(5);
 * // 返回: "05:00~05:59"
 *
 * @example
 * // 示例2：处理两位数小时
 * const timeRange2 = formatHourToTimeRange(15);
 * // 返回: "15:00~15:59"
 *
 * @example
 * // 示例3：处理0点
 * const timeRange3 = formatHourToTimeRange(0);
 * // 返回: "00:00~00:59"
 */
export const formatHourToTimeRange = (hour: number) => {
  let h = '';
  if (hour < 10) {
    h = '0' + hour;
  } else {
    h = hour.toString();
  }

  return h + ':00~' + h + ':59';
};

// 禁用今天之前的所有日期
export const disabledDate = (current: dayjs.Dayjs) => {
  return current && current < dayjs().startOf('day');
};
// 生成数字区间数组的辅助函数
const range = (start: number, end: number) => {
  const result: number[] = [];
  for (let i = start; i <= end; i++) {
    result.push(i);
  }
  return result;
};

//禁用今天所有的时间
export const disabledDateTime = (current: dayjs.Dayjs) => {
  // 如果不是今天，不禁用任何时间
  if (!current || !current.isSame(dayjs(), 'day')) {
    return {};
  }

  const now = dayjs();

  return {
    disabledHours: () => range(0, now.hour() - 1),
    disabledMinutes: (selectedHour: number) => {
      if (selectedHour < now.hour()) return range(0, 59);
      return range(0, now.minute() - 1);
    },
    disabledSeconds: (selectedHour: number, selectedMinute: number) => {
      if (selectedHour < now.hour()) return range(0, 59);
      if (selectedMinute < now.minute()) return range(0, 59);
      return range(0, now.second() - 1);
    },
  };
};

// 封装一个函数，生成当前日期的开始时间和结束时间
export const getTodayTimeRange = () => {
  const start = dayjs().startOf('day').format('x');
  const end = dayjs().endOf('day').format('x');
  return [start, end];
};

/**
 * 获取当前时间 n 天后的 dayjs 对象（用于 Ant Design DatePicker 的 presets 快捷选项）
 *
 * @param days 要增加的天数（可为负值表示向前推）
 * @returns 返回一个 dayjs 对象，表示 n 天后的时间
 */
export function getDateAfterNDays(days: number): dayjs.Dayjs {
  return dayjs().add(days, 'day');
}

/**
 * 获取当前时刻的时间范围
 *
 * @param format 格式化字符串：
 *               ''    - 返回 dayjs 对象
 *               'X'   - 返回 10 位 Unix 时间戳（秒）
 *               'x'   - 返回 13 位毫秒时间戳
 *               其他   - 按 dayjs.format(format) 输出
 * @returns [current, current]，格式化后的数组，开始和结束时间都是当前时刻
 */
export function getCurrentMomentRange(
  format: '' | 'X' | 'x' | string = '',
): Array<number | string | dayjs.Dayjs> {
  // 当前时刻
  const current = dayjs();

  const formatter: {
    [key: string]: (d: dayjs.Dayjs) => number | string | dayjs.Dayjs;
    default: (d: dayjs.Dayjs) => dayjs.Dayjs | string;
  } = {
    X: (d) => d.unix(),
    x: (d) => d.valueOf(),
    default: (d) => (format ? d.format(format) : d),
  };

  const fn = formatter[format] || formatter.default;
  // 返回相同的当前时刻作为开始和结束时间
  return [current, current].map(fn);
}
