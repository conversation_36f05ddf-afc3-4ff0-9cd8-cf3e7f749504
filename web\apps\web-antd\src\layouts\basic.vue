<script lang="ts" setup>
import { computed, watch, useTemplateRef, getCurrentInstance } from 'vue';

import { AuthenticationLoginExpiredModal } from '@vben/common-ui';
import { useWatermark } from '@vben/hooks';
import { User, Share, CloudUpload } from '@vben/icons';
import {
  BasicLayout,
  LockScreen,
  UserDropdown,
} from '@vben/layouts';
import { preferences, usePreferences } from '@vben/preferences';
import { useAccessStore } from '@vben/stores';
import { useUserStore } from '../store';
import { useVbenModal } from '@vben/common-ui';
import ReleaseTicketModal from '../components/modal/release-ticket-modal.vue';
import ImportTicketModal from '../components/modal/import-ticket-modal.vue';
import UserSettingsModal from '../components/modal/user-settings-modal.vue';
import NotificationPopup from '../components/notification/notification-popup.vue';

import { $t } from '#/locales';
import { useAuthStore } from '#/store';
import LoginForm from '#/views/_core/authentication/login.vue';
import SwitchDev from '#/components/switch-dev/index.vue';
import HighLight from '#/components/high-light/index.vue';

const userStore = useUserStore();
const authStore = useAuthStore();
const accessStore = useAccessStore();
const { updateWatermark, updateBlindWatermark } = useWatermark();
const blindWatermarkContainer = useTemplateRef('blindWatermarkContainer');

// 获取当前实例，用于访问全局属性
const instance = getCurrentInstance();
const $isShowOpt = (optPerm: string, isHeader = false) => {
  // 使用类型断言来访问全局属性
  return (instance?.proxy as any)?.$isShowOpt?.(optPerm, isHeader) || false;
};

const [ReleaseTicketModalComponent, releaseTicketModalApi] = useVbenModal({
  connectedComponent: ReleaseTicketModal,
});

const [ImportTicketModalComponent, importTicketModalApi] = useVbenModal({
  connectedComponent: ImportTicketModal,
});

const [UserSettingsModalComponent, userSettingsModalApi] = useVbenModal({
  connectedComponent: UserSettingsModal,
});

const menus = computed(() => [
  {
    handler: () => {
      userSettingsModalApi.open();
    },
    icon: User,
    text: $t('ui.widgets.ticket.setting'),
  },
  ...($isShowOpt('update', true)
      ? [{
          handler: () => releaseTicketModalApi.open(),
          icon: Share,
          text: $t('ui.widgets.ticket.release'),
        }]
      : []),
  {
    handler: () => {
      importTicketModalApi.open();
    },
    icon: CloudUpload,
    text: $t('ui.widgets.ticket.import'),
  },
]);

const avatar = computed(() => preferences.app.defaultAvatar);

async function handleLogout() {
  await authStore.logout();
}
const { isDark } = usePreferences();
watch(
  // 水印
  () => preferences.app.watermark,
  async () => {
    const currentDate = new Date();
    const formattedDate = `${currentDate.getFullYear()}-${currentDate.getMonth() + 1}-${currentDate.getDate()}`;
    const realName = userStore.realname;
    await updateWatermark({
      fontColor: 'gray',
      content: `${formattedDate}\n${realName}`,
    });
    if (blindWatermarkContainer.value) {
      await updateBlindWatermark({
        fontColor: isDark.value ? '#fff' : '#000',
        content: `${formattedDate}\n${realName}`,
        parent: blindWatermarkContainer.value,
        globalAlpha: 100,
      });
    }
  },
  {
    immediate: true,
  },
);
</script>

<template>
  <BasicLayout @clear-preferences-and-logout="handleLogout">
    <template #user-dropdown>
      <UserDropdown
        :avatar
        :menus
        :text="userStore.realname"
        :description="userStore.username"
        :tag-text="userStore.supplier"
        @logout="handleLogout"
      />
    </template>
    <template #notification>
      <NotificationPopup />
    </template>
    <template #extra>
      <AuthenticationLoginExpiredModal
        v-model:open="accessStore.loginExpired"
        :avatar
      >
        <LoginForm />
      </AuthenticationLoginExpiredModal>
      <ReleaseTicketModalComponent />
      <ImportTicketModalComponent />
      <UserSettingsModalComponent />
    </template>
    <template #lock-screen>
      <LockScreen :avatar @to-login="handleLogout" />
    </template>
    <template #header-right-51>
      <HighLight />
    </template>
    <template #header-right-49>
      <SwitchDev />
    </template>
  </BasicLayout>
  <div ref="blindWatermarkContainer"></div>
</template>
