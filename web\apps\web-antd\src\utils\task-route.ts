/**
 * 根据任务名称获取对应的路由名称
 * @param taskName 任务名称
 * @returns 路由名称
 */
export function taskRoute(taskName: string): string {
  // 任务名称到路由名称的映射
  const taskRouteMap: Record<string, string> = {
    // 文档审核相关
    'doc_audit': 'doc-audit',
    'doc_review': 'doc-review',
    'text_audit': 'text-audit',
    'text_review': 'text-review',
    
    // 图片审核相关
    'image_audit': 'image-audit',
    'image_review': 'image-review',
    'cv_audit': 'cv-audit',
    'cv_review': 'cv-review',
    
    // 账号审核相关
    'account_audit': 'account-audit',
    'account_review': 'account-review',
    
    // 其他任务类型
    'ai_mark': 'ai-mark',
    'manual_mark': 'manual-mark',
    'quality_check': 'quality-check',
  };

  return taskRouteMap[taskName] || taskName;
}

/**
 * 复制文本到剪贴板
 * @param text 要复制的文本
 * @returns Promise<boolean> 是否复制成功
 */
export async function copyToClipboard(text: string): Promise<boolean> {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text);
      return true;
    } else {
      // 降级方案：使用 document.execCommand
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      const result = document.execCommand('copy');
      document.body.removeChild(textArea);
      return result;
    }
  } catch (error) {
    console.error('复制失败:', error);
    return false;
  }
}

/**
 * 自动复制元素的文本内容
 * @param element DOM 元素
 */
export function autoCopy(element: HTMLElement): void {
  const text = element.textContent || element.innerText || '';
  copyToClipboard(text);
}
