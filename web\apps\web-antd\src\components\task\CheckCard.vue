<script lang="ts" setup>
import type { PullTaskRequest, TaskItem } from '#/types/task';

import { onMounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';

import { useMagicKeys } from '@vueuse/core';
import { message } from 'ant-design-vue';
import { storeToRefs } from 'pinia';

import { pullTasks } from '#/api/task';
import {
  useLoadingState,
  ViewStatus,
} from '#/composables/ui/use-loading-state';
import { $t } from '#/locales';
import { useTaskStore } from '#/store';
import useWebSocketStore from '#/store/websocket';

import CheckTabList from './CheckTabList.vue';
import FileView from './FileView.vue';
// 控制弹窗显示

const props = withDefaults(
  defineProps<{
    dataAdapter?: (item: any) => TaskItem;
    pullApi?: () => Promise<{ records: TaskItem[]; total: number } | undefined>;
    tabListRender?: (item: any) => any;
  }>(),
  {
    pullApi: undefined,
    dataAdapter: (item: any) => item,
    tabListRender: undefined,
  },
);
// 事件
const emit = defineEmits(['finish', 'changeTask', 'tab-change']);
const websocketStore = useWebSocketStore();
const taskStore = useTaskStore();
const { taskItem } = storeToRefs(taskStore);
const { deletedTaskIds, releaseTask } = storeToRefs(websocketStore);
const {
  status,
  isLoading,
  isUnloaded,
  setLoading,
  isLoadFailed,
  setLoaded,
  setLoadFailed,
  setCompleteLoaded,
  reset,
} = useLoadingState(ViewStatus.UNLOAD);
const { biz, queue, limit } = storeToRefs(taskStore);
const taskList = ref<TaskItem[]>([]); // 任务列表
const tabIndex = ref(1);
const route = useRoute();

// 弹窗相关
const showWarningModal = ref(false); /**
 * 获取任务数据的函数
 * @returns {Promise<{total: number; records: TaskItem[]} | undefined>} 返回任务数据或undefined
 */
const fetchTasks = function (): Promise<
  { records: TaskItem[]; total: number } | undefined
> {
  if (props.pullApi) {
    return props.pullApi();
  }

  return new Promise((resolve) => {
    if (!queue.value) {
      resolve(undefined);
      return;
    }

    const params: PullTaskRequest = {
      biz: biz.value,
      limit: limit.value,
      offset: 0,
      entry: queue.value,
    };
    if (route.name === 'UrgentTask') {
      params.type_view = 2;
    }
    pullTasks(params)
      .then((res) => {
        resolve({
          total: res?.data?.count || 0,
          records: res?.data?.records || [],
        });
      })
      .catch(() => {
        resolve(undefined);
      });
  });
};

const handleTaskChange = async (taskItem: any) => {
  taskStore.disabledManual = true;
  if (!taskItem) return;
  setLoading();
  emit('tab-change', taskItem);
  taskStore.setTaskItem(props.dataAdapter(taskItem));
  emit('changeTask', taskItem);
  setLoaded();
};

// 加载一批文件
const loadFileTask = async () => {
  setLoading();
  try {
    const res = await fetchTasks();
    if (!res) {
      reset();
      return;
    }
    const result = res?.records.map((item: TaskItem, index: number) => ({
      ...item,
      index: ++index,
    })); // 给每一项加上index属性
    taskStore.setCounter({
      total: res?.total || 0,
      load: res?.records.length || 0,
      finished: 0,
    });
    taskList.value = result || [];
    // 如果任务列表为空,则提示没有更多文件了
    if (taskList.value.length <= 0) {
      message.info('没有更多文件了~');
      reset();
      return;
    }
    getNextTask(false);
  } catch {
    setLoadFailed();
    message.error('加载任务失败');
  }
};

// 审核当前单完毕自动切到下一单
const getNextTask = async (isNext: boolean) => {
  if (isNext) {
    tabIndex.value++;
  }

  // 查找下一个未审核的任务（buttonType为空的任务）
  const findNextUncheckedTask = () => {
    for (let i = 0; i < taskList.value.length; i++) {
      if (!taskList.value[i]?.buttonType) {
        return i + 1; // 返回1-based的索引
      }
    }
    return -1; // 没有找到未审核的任务
  };

  // 如果当前批次所有任务都已完成，重置状态
  const nextUncheckedIndex = findNextUncheckedTask();
  if (nextUncheckedIndex === -1) {
    reset();
    taskStore.resetCounter();
    taskList.value = [];
    taskStore.taskItem = undefined;
    taskStore.disabledManual = true;
    return;
  }

  // 如果当前索引超出范围或当前任务已审核，切换到下一个未审核的任务
  if (
    tabIndex.value > taskList.value.length ||
    taskList.value[tabIndex.value - 1]?.buttonType
  ) {
    tabIndex.value = nextUncheckedIndex;
  }

  taskStore.resetCardClickTime();
  // 设置当前任务项
  emit('tab-change', taskList.value[tabIndex.value - 1]);
  taskStore.taskItem = props.dataAdapter(taskList.value[tabIndex.value - 1]);
  console.log('taskItemA', taskStore.taskItem);
  setLoaded();
};

const handleLoaded = (viewType?: number) => {
  setCompleteLoaded();
  taskStore.isView = true;
  taskStore.resetFinishedRenderTime();
  emit('finish', viewType);
};

const handleLoadFail = (viewType?: number) => {
  setLoadFailed();
  taskStore.isView = false;
  console.log('handleLoadFail', status.value, isLoadFailed.value);
  taskStore.resetFinishedRenderTime();
  emit('finish', -(viewType ?? 0));
};

/**
 * 关闭警告弹窗
 */
const closeWarningModal = () => {
  showWarningModal.value = false;
};

/**
 * 监听queue和biz
 */
watch(
  () => [
    queue.value,
    biz.value,
    releaseTask.value,
    taskStore.releaseTaskTrigger,
  ],
  () => {
    reset();
    tabIndex.value = 1;
    taskStore.taskItem = undefined;
  },
);
watch(releaseTask, (newVal) => {
  if (newVal === biz.value) {
    message.info(`检测到工单释放`);
    reset();
    taskStore.taskItem = undefined;
    tabIndex.value = 1;
    taskStore.resetCounter();
    taskStore.disabledManual = true;
  }
});
watch(deletedTaskIds, (newVal) => {
  if (newVal.length <= 0) return;
  console.log('deletedTaskIds', newVal);
  taskList.value = taskList.value.filter(
    (item: TaskItem) => !newVal.includes(item.id),
  );
  // 对taskList的index进行重写编码
  taskList.value.forEach((item: TaskItem, index: number) => {
    item.index = index + 1;
  });
  if (taskList.value.length <= 0) {
    message.info('没有更多文件了~');
    reset();
    return;
  }
  getNextTask(false);
  taskStore.counter.total--;
});
watch(taskItem, (newVal) => {
  taskStore.setSelectedForbidType(newVal?.doc_type);
});

/**
 * 使用空格键快捷键加载下一批文件
 *
 * 示例：
 * 按下空格键：下载下一批文件
 */
const useSpaceKeyShortcut = () => {
  const { current } = useMagicKeys({
    passive: false,
    onEventFired(e) {
      // 当按下空格键且组件处于未加载状态时，阻止默认行为
      if (e.key === ' ' && e.type === 'keydown' && isUnloaded.value) {
        e.preventDefault();
      }
    },
  });

  watch(current, (v) => {
    if (v.size > 0) {
      const key = [...v].join('+');
      if (key === ' ' && isUnloaded.value) {
        loadFileTask();
      }
    }
  });
};

// 初始化空格键快捷键
useSpaceKeyShortcut();
onMounted(() => {
  console.log('route.name', route.name);
  if (route.name === 'UrgentTask') {
    showWarningModal.value = true;
  }
});
defineExpose({
  getNextTask, // 暴露给父级使用，审核完某条单后自动切单
  loadFileTask, // 暴露加载文件任务方法
});
</script>

<template>
  <div class="flex flex-col">
    <div
      class="view-container relative flex h-full max-h-[calc(100%-4rem)] flex-1 flex-col items-center justify-center border"
    >
      <!-- 未加载状态 -->
      <div
        v-if="isUnloaded"
        class="flex h-full flex-1 items-center justify-center"
      >
        <a-button type="link" @click="loadFileTask">
          {{ $t('page.checkList.task.loadNext') }}
        </a-button>
      </div>

      <!-- 加载中状态 -->
      <div
        v-else-if="isLoading"
        class="flex flex-1 items-center justify-center"
      >
        <a-spin />
      </div>
      <!-- 将biz,queue,taskItem?.id,tabIndex作为key,确保这些变量变化时，组件重新渲染 -->
      <!-- <media-view
        v-if="
          mediaType !== MediaType.NotSupport &&
          mediaType !== MediaType.NotInit &&
          !isUnloaded
        "
        :key="`media-view-${biz}-${queue}-${taskItem?.id}-${tabIndex}`"
        :media-type="mediaType"
        :video-urls="[]"
        class="media flex h-full w-full flex-1 flex-col"
        @load-fail="handleLoadFail"
        @loaded="handleLoaded"
      /> -->
      <file-view
        v-if="!isUnloaded && !isLoading"
        :key="`file-view-${biz}-${queue}-${taskItem?.id}-${tabIndex}`"
        class="file flex h-full w-full flex-1 flex-col"
        @load-fail="handleLoadFail"
        @loaded="handleLoaded"
      />
    </div>
    <check-tab-list
      v-if="!isUnloaded"
      v-model:index="tabIndex"
      :options="taskList"
      :render="tabListRender"
      class="mb-4"
      @change="handleTaskChange"
    />

    <!-- 紧急任务提示弹窗 -->
    <a-modal
      v-model:open="showWarningModal"
      :centered="true"
      :closable="true"
      :footer="null"
      :mask-closable="false"
      :width="500"
      title="注意"
      @cancel="closeWarningModal"
    >
      <p class="text-red-500">
        从该入口领取提单，数据来源之前，必须将之前数据清理完毕，方可拉取新数据，反之离开之前也需清理完毕！
      </p>
      <div class="mt-4 text-right">
        <a-button type="primary" @click="closeWarningModal">确定</a-button>
      </div>
    </a-modal>
  </div>
</template>
