<script lang="ts" setup>
import type { BizConfig } from '#/types/platform';
import type { TaskConfig } from '#/types/task';

import { computed, inject, ref, type Ref, watch, getCurrentInstance } from 'vue';

import { useTaskStore } from '#/store';
import usePlatformStore from '#/store/platform';
import useWebSocketStore from '#/store/websocket';

interface Props {
  diabledBadge?: boolean;
}
const props = withDefaults(defineProps<Props>(), {
  diabledBadge: false,
});
// 业务方改变
const emit = defineEmits(['change', 'sendCurOption']);
const platformStore = usePlatformStore();
const taskStore = useTaskStore();
const websocketStore = useWebSocketStore();
// 接收外部传入的值
const value = defineModel<string>('value');
const originalBizOptions = inject<Ref<BizConfig[]>>('BizOptions') || ref<BizConfig[]>([]);

// 获取当前实例，用于访问全局属性
const instance = getCurrentInstance();
const $isShowPlat = (platPerm: string): boolean => {
  return (instance?.proxy as any)?.$isShowPlat?.(platPerm) || false;
};

// 过滤后的业务选项，根据权限过滤
const bizOptions = computed(() => {
  return originalBizOptions.value.filter(item => $isShowPlat(item.biz));
});
const bizChange = (value: string) => {
  emit('change', value); // 后续整理看这个是否去掉
  const curOption = bizOptions.value.find((item) => item.biz === value);
  emit('sendCurOption', curOption);
  taskStore.biz = value;
  platformStore.biz = value;
};
// 计算每一个业务biz下所有的队列的数量的和
const countBizTotal = computed(() => (biz: string) => {
  if (props.diabledBadge) {
    return 0;
  }
  if (!bizOptions?.value) {
    return 0;
  }
  const queueArr =
    (bizOptions.value.find((item) => item.biz === biz)
      ?.task_names as TaskConfig[]) || [];
  // 上述为判空逻辑
  return queueArr.reduce((acc, { task_name }) => {
    return (
      acc + (websocketStore.queueCountDelayData?.[biz]?.[task_name]?.num || 0)
    );
  }, 0);
});
// 计算当前页面所有业务下的所有队列的熟练的和
const countPageTotal = computed(() => {
  if (!bizOptions?.value) {
    return 0;
  }

  return bizOptions.value.reduce((total, it) => {
    const queueArr = it.task_names as TaskConfig[];
    const bizTotal = queueArr.reduce((acc, { task_name }) => {
      return (
        acc +
        (websocketStore.queueCountDelayData?.[it.biz]?.[task_name]?.num || 0)
      );
    }, 0);
    return total + bizTotal;
  }, 0);
});
watch(
  bizOptions,
  (newVal: any) => {
    if (newVal?.length > 0) {
      // 给 activeKey 赋初始值为第一个biz
      taskStore.biz = newVal[0].biz;
      platformStore.biz = newVal[0].biz;
      value.value = newVal[0].biz;
    }
  },
  { immediate: true }, // 立即执行一次，确保初始赋值
);
// 暴露数据给父组件
defineExpose({
  countPageTotal,
});
</script>
<template>
  <!-- type='card' 参数时  tabBarGutter = 5 :offset="[5, -3]后续看是否需要该样式-->
  <a-tabs v-model:active-key="value" :tab-bar-gutter="45" @change="bizChange">
    <a-tab-pane v-for="it in bizOptions" :key="it.biz">
      <template #tab>
        <a-badge
          :count="countBizTotal(it.biz)"
          :offset="[25, -3]"
          :overflow-count="999999"
        >
          <span>{{ it.biz_alias }}</span>
        </a-badge>
      </template>
    </a-tab-pane>
    <!-- <template #rightExtra>
      <a-popover trigger="click" v-model:open="select.visible" placement="bottom">
        <search-icon @click="select.visible = true" class="size-5"></search-icon>
        <template #content>
          <v-select @change="handleChange" class="w-[200px]" show-search>
            <a-select-option class="w-[200px]" v-for="option in filteredOptions" :key="option.id"
              :value="option.platform">
              {{ option.detail }}
            </a-select-option>
          </v-select>
        </template>
      </a-popover>
    </template> -->
  </a-tabs>
</template>
<style scoped>
:deep(.ant-tabs-nav) {
  margin-bottom: 0px;
}
:deep(.ant-tabs-nav-list) {
  margin-right: 10px;
}
</style>

<!-- 发现antd的tabs栏下拉菜单使用的是全局的位置不包含在该组件内，使用scoped不生效，需要使用非scoped样式来确保能覆盖Ant Design的样式 -->
<style>
/* 增加下拉菜单的行高，确保badge能够正常显示 */
.ant-tabs-dropdown .ant-dropdown-menu-item,
.ant-tabs-dropdown-menu-item,
.ant-dropdown-menu-item,
.ant-dropdown-menu-item-only-child {
  overflow: visible !important;
  width: 180px !important;
  line-height: 30px !important;
}
</style>
<style scoped>
:deep(.ant-tabs-nav){
  min-width: 100px !important;
}
</style>
