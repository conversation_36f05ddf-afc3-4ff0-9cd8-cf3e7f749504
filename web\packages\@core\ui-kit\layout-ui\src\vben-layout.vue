<script setup lang="ts">
import type { VbenLayoutProps } from './vben-layout';

import type { CSSProperties } from 'vue';
import { computed, ref, watch } from 'vue';

import {
  SCROLL_FIXED_CLASS,
  useLayoutFooterStyle,
  useLayoutHeaderStyle,
} from '@vben-core/composables';
import { Menu } from '@vben-core/icons';
import { VbenIconButton } from '@vben-core/shadcn-ui';
import { ELEMENT_ID_MAIN_CONTENT } from '@vben-core/shared/constants';

import { useMouse, useScroll, useThrottleFn } from '@vueuse/core';

import {
  LayoutContent,
  LayoutFooter,
  LayoutHeader,
  LayoutSidebar,
} from './components';
import { useLayout } from './hooks/use-layout';

interface Props extends VbenLayoutProps { }

defineOptions({
  name: 'VbenLayout',
});

const props = withDefaults(defineProps<Props>(), {
  contentCompact: 'wide',
  contentCompactWidth: 1200,
  contentPadding: 0,
  contentPaddingBottom: 0,
  contentPaddingLeft: 0,
  contentPaddingRight: 0,
  contentPaddingTop: 0,
  footerEnable: false,
  footerFixed: true,
  footerHeight: 32,
  headerHeight: 50,
  headerHidden: false,
  headerMode: 'fixed',
  headerToggleSidebarButton: true,
  headerVisible: true,
  isMobile: false,
  layout: 'sidebar-nav',
  sidebarCollapseShowTitle: false,
  sidebarExtraCollapsedWidth: 60,
  sidebarHidden: false,
  sidebarMixedWidth: 80,
  sidebarTheme: 'dark',
  sidebarWidth: 180,
  sideCollapseWidth: 60,
  showCollapseButton: true,
  showFixedButton: true,
  zIndex: 200,
});

const emit = defineEmits<{ sideMouseLeave: []; toggleSidebar: [] }>();
const sidebarCollapse = defineModel<boolean>('sidebarCollapse');
const sidebarExtraVisible = defineModel<boolean>('sidebarExtraVisible');
const sidebarExtraCollapse = defineModel<boolean>('sidebarExtraCollapse');
const sidebarExpandOnHover = defineModel<boolean>('sidebarExpandOnHover');
const sidebarEnable = defineModel<boolean>('sidebarEnable', { default: true });

// side是否处于hover状态展开菜单中
const sidebarExpandOnHovering = ref(false);
const headerIsHidden = ref(false);
const contentRef = ref();

const {
  arrivedState,
  directions,
  isScrolling,
  y: scrollY,
} = useScroll(document);

const { setLayoutHeaderHeight } = useLayoutHeaderStyle();
const { setLayoutFooterHeight } = useLayoutFooterStyle();

const { y: mouseY } = useMouse({ target: contentRef, type: 'client' });

const {
  currentLayout,
  isFullContent,
  isHeaderNav,
  isMixedNav,
  isSidebarMixedNav,
} = useLayout(props);

/**
 * 顶栏是否自动隐藏
 */
const isHeaderAutoMode = computed(() => props.headerMode === 'auto');

const headerWrapperHeight = computed(() => {
  return props.headerVisible && !props.headerHidden
    ? props.headerHeight
    : 0;
});

const getSideCollapseWidth = computed(() => {
  const { sidebarCollapseShowTitle, sidebarMixedWidth, sideCollapseWidth } =
    props;

  return sidebarCollapseShowTitle || isSidebarMixedNav.value
    ? sidebarMixedWidth
    : sideCollapseWidth;
});

/**
 * 动态获取侧边区域是否可见
 */
const sidebarEnableState = computed(() => {
  return !isHeaderNav.value && sidebarEnable.value;
});

/**
 * 侧边区域离顶部高度
 */
const sidebarMarginTop = computed(() => {
  const { headerHeight, isMobile } = props;
  return isMixedNav.value && !isMobile ? headerHeight : 0;
});

/**
 * 动态获取侧边宽度
 */
const getSidebarWidth = computed(() => {
  const { isMobile, sidebarHidden, sidebarMixedWidth, sidebarWidth } = props;
  let width = 0;

  if (sidebarHidden) {
    return width;
  }

  if (
    !sidebarEnableState.value ||
    (sidebarHidden && !isSidebarMixedNav.value && !isMixedNav.value)
  ) {
    return width;
  }

  if (isSidebarMixedNav.value && !isMobile) {
    width = sidebarMixedWidth;
  } else if (sidebarCollapse.value) {
    width = isMobile ? 0 : getSideCollapseWidth.value;
  } else {
    width = sidebarWidth;
  }
  return width;
});

/**
 * 获取扩展区域宽度
 */
const sidebarExtraWidth = computed(() => {
  const { sidebarExtraCollapsedWidth, sidebarWidth } = props;

  return sidebarExtraCollapse.value ? sidebarExtraCollapsedWidth : sidebarWidth;
});

/**
 * 是否侧边栏模式，包含混合侧边
 */
const isSideMode = computed(
  () =>
    currentLayout.value === 'mixed-nav' ||
    currentLayout.value === 'sidebar-mixed-nav' ||
    currentLayout.value === 'sidebar-nav',
);

/**
 * header fixed值
 */
const headerFixed = computed(() => {
  const { headerMode } = props;
  return (
    isMixedNav.value ||
    headerMode === 'fixed' ||
    headerMode === 'auto-scroll' ||
    headerMode === 'auto'
  );
});

const showSidebar = computed(() => {
  return isSideMode.value && sidebarEnable.value && !props.sidebarHidden;
});

/**
 * 遮罩可见性
 */
const maskVisible = computed(() => !sidebarCollapse.value && props.isMobile);

const mainStyle = computed(() => {
  let width = '100%';
  let sidebarAndExtraWidth = 'unset';
  if (
    headerFixed.value &&
    currentLayout.value !== 'header-nav' &&
    currentLayout.value !== 'mixed-nav' &&
    showSidebar.value &&
    !props.isMobile
  ) {
    // fixed模式下生效
    const isSideNavEffective =
      isSidebarMixedNav.value &&
      sidebarExpandOnHover.value &&
      sidebarExtraVisible.value;

    if (isSideNavEffective) {
      const sideCollapseWidth = sidebarCollapse.value
        ? getSideCollapseWidth.value
        : props.sidebarMixedWidth;
      const sideWidth = sidebarExtraCollapse.value
        ? props.sidebarExtraCollapsedWidth
        : props.sidebarWidth;

      // 100% - 侧边菜单混合宽度 - 菜单宽度
      sidebarAndExtraWidth = `${sideCollapseWidth + sideWidth}px`;
       width = `calc(100% - ${Math.min(getSidebarWidth.value, 200)}px)`
    } else {
      sidebarAndExtraWidth =
        sidebarExpandOnHovering.value && !sidebarExpandOnHover.value
          ? `${getSideCollapseWidth.value}px`
          : `${getSidebarWidth.value}px`;
       width = `calc(100% - ${Math.min(getSidebarWidth.value, 200)}px)`
    }
  }
  return {
    sidebarAndExtraWidth,
    width,
  };
});


const contentStyle = computed((): CSSProperties => {
  const fixed = headerFixed.value;

  const { footerEnable, footerFixed, footerHeight } = props;
  return {
    marginTop:
      fixed &&
        !isFullContent.value &&
        !headerIsHidden.value &&
        (!isHeaderAutoMode.value || scrollY.value < headerWrapperHeight.value)
        ? `${headerWrapperHeight.value}px`
        : 0,
    paddingBottom: `${footerEnable && footerFixed ? footerHeight : 0}px`,
  };
});

const headerZIndex = computed(() => {
  const { zIndex } = props;
  const offset = isMixedNav.value ? 1 : 0;
  return zIndex + offset;
});

const headerWrapperStyle = computed((): CSSProperties => {
  const fixed = headerFixed.value;
  return {
    height: isFullContent.value ? '0' : `${headerWrapperHeight.value}px`,
    left: isMixedNav.value ? 0 : mainStyle.value.sidebarAndExtraWidth,
    position: fixed ? 'fixed' : 'static',
    top:
      headerIsHidden.value || isFullContent.value
        ? `-${headerWrapperHeight.value}px`
        : 0,
    width: mainStyle.value.width,
    'z-index': headerZIndex.value,
  };
});

/**
 * 侧边栏z-index
 */
const sidebarZIndex = computed(() => {
  const { isMobile, zIndex } = props;
  let offset = isMobile || isSideMode.value ? 1 : -1;

  if (isMixedNav.value) {
    offset += 1;
  }

  return zIndex + offset;
});

const footerWidth = computed(() => {
  if (!props.footerFixed) {
    return '100%';
  }

  return mainStyle.value.width;
});

const maskStyle = computed((): CSSProperties => {
  return { zIndex: props.zIndex };
});

const showHeaderToggleButton = computed(() => {
  return (
    props.isMobile ||
    (props.headerToggleSidebarButton &&
      isSideMode.value &&
      !isSidebarMixedNav.value &&
      !isMixedNav.value &&
      !props.isMobile)
  );
});

const showHeaderLogo = computed(() => {
  return !isSideMode.value || isMixedNav.value || props.isMobile;
});

watch(
  () => props.isMobile,
  (val) => {
    if (val) {
      sidebarCollapse.value = true;
    }
  },
  {
    immediate: true,
  },
);

watch(
  [() => headerWrapperHeight.value, () => isFullContent.value],
  ([height]) => {
    setLayoutHeaderHeight(isFullContent.value ? 0 : height);
  },
  {
    immediate: true,
  },
);

watch(
  () => props.footerHeight,
  (height: number) => {
    setLayoutFooterHeight(height);
  },
  {
    immediate: true,
  },
);

{
  const mouseMove = () => {
    mouseY.value > headerWrapperHeight.value
      ? (headerIsHidden.value = true)
      : (headerIsHidden.value = false);
  };
  watch(
    [() => props.headerMode, () => mouseY.value],
    () => {
      if (!isHeaderAutoMode.value || isMixedNav.value || isFullContent.value) {
        if (props.headerMode !== 'auto-scroll') {
          headerIsHidden.value = false;
        }
        return;
      }
      headerIsHidden.value = true;
      mouseMove();
    },
    {
      immediate: true,
    },
  );
}

{
  const checkHeaderIsHidden = useThrottleFn((top, bottom, topArrived) => {
    if (scrollY.value < headerWrapperHeight.value) {
      headerIsHidden.value = false;
      return;
    }
    if (topArrived) {
      headerIsHidden.value = false;
      return;
    }

    if (top) {
      headerIsHidden.value = false;
    } else if (bottom) {
      headerIsHidden.value = true;
    }
  }, 300);

  watch(
    () => scrollY.value,
    () => {
      if (
        props.headerMode !== 'auto-scroll' ||
        isMixedNav.value ||
        isFullContent.value
      ) {
        return;
      }
      if (isScrolling.value) {
        checkHeaderIsHidden(
          directions.top,
          directions.bottom,
          arrivedState.top,
        );
      }
    },
  );
}

function handleClickMask() {
  sidebarCollapse.value = true;
}

function handleHeaderToggle() {
  if (props.isMobile) {
    sidebarCollapse.value = false;
  } else {
    emit('toggleSidebar');
  }
}

const idMainContent = ELEMENT_ID_MAIN_CONTENT;
</script>

<template>
  <div class="relative flex min-h-full w-full">
    <LayoutSidebar v-if="sidebarEnableState" v-model:collapse="sidebarCollapse"
      v-model:expand-on-hover="sidebarExpandOnHover" v-model:expand-on-hovering="sidebarExpandOnHovering"
      v-model:extra-collapse="sidebarExtraCollapse" v-model:extra-visible="sidebarExtraVisible"
      :collapse-width="getSideCollapseWidth" :dom-visible="!isMobile" :extra-width="sidebarExtraWidth"
      :fixed-extra="sidebarExpandOnHover" :header-height="isMixedNav ? 0 : headerHeight"
      :is-sidebar-mixed="isSidebarMixedNav" :margin-top="sidebarMarginTop" :mixed-width="sidebarMixedWidth"
      :show="showSidebar" :show-collapse-button="showCollapseButton" :show-fixed-button="showFixedButton"
      :theme="sidebarTheme" :width="getSidebarWidth" :z-index="sidebarZIndex"
      @leave="() => emit('sideMouseLeave')">
      <template v-if="isSideMode && !isMixedNav" #logo>
        <div class="sys-title" tabindex="0">
          <div class="title-img">
            <svg x="0" y="0" width="36" height="39.676907394743296" filtersec="colorsb8814029989"
              class="image-svg-svg primary" style="overflow: visible">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 131.24000549316406 84.66999816894531">
                <g fill-rule="evenodd">
                  <path fill="#febd00" d="M88.31 0L65.62 24.19 42.93 0l22.69 84.67L88.31 0z"></path>
                  <path fill="#ed4030" d="M16.88 84.67l5.3-13.13L0 46.02l59.22 38.65H16.88z"></path>
                  <path fill="#f79e1c" d="M24.42 46.02l6.69-7.92-6.69-30.72 38.5 77.29-38.5-38.65z"></path>
                  <path fill="#0088d2" d="M114.37 84.67l-5.3-13.13 22.17-25.52-59.22 38.65h42.35z"></path>
                  <path fill="#01b6ad" d="M106.83 46.02l-6.7-7.92 6.7-30.72-38.51 77.29 38.51-38.65z"></path>
                </g>
              </svg>
            </svg>
          </div>
          <div v-show="!sidebarCollapse" class="title-name">
            <svg data-v-6d8cc87e="" xmlns="http://www.w3.org/2000/svg" viewBox="-12 -9 130 40" width="84" height="42"
              style="overflow: visible">
              <g fill="rgb(235,235,235)">
                <path xmlns="http://www.w3.org/2000/svg"
                  d="M13.21-26.32L13.21-26.32 13.32-26.64Q13.54-27.07 13.99-26.88 14.45-26.69 14.82-26.32L14.82-26.32Q15.09-25.67 15.5-25.22 15.9-24.76 16.54-24.92L16.54-24.92Q17.35-24.98 17.48-24.44 17.62-23.9 17.83-23.31L17.83-23.31Q17.67-22.45 17.4-21.62 17.13-20.79 16.65-19.87L16.65-19.87Q15.15-18.96 13.62-20.17 12.08-21.38 12.25-23.1L12.25-23.1Q12.03-24.17 12.19-24.73 12.35-25.3 13.21-26.32ZM27.39-27.61L27.39-27.61 27.07-27.5Q26.32-27.07 25.75-26.53 25.19-26 24.81-25.14L24.81-25.14Q24.6-24.55 24.9-24.38 25.19-24.22 25.78-24.6L25.78-24.6Q26.32-25.19 27.12-26.24 27.93-27.29 27.39-27.61ZM39.75-35.34L40.93-36.42Q42.86-37.06 43.64-36.25 44.42-35.45 44.04-33.52L44.04-33.52Q42.92-30.67 41.89-28.06 40.87-25.46 40.93-22.45L40.93-22.45Q41.52-21.48 42.24-20.52 42.97-19.55 43.18-18.58L43.18-18.58Q42.54-16.06 41.89-13.83 41.25-11.6 40.28-9.02L40.28-9.02Q40.12-8.16 40.31-7.84 40.5-7.52 41.36-6.98L41.36-6.98Q42.86-6.45 44.2-5.64 45.55-4.83 46.94-3.22L46.94-3.22 47.59-2.15Q48.66 0.05 47 1.53 45.33 3.01 43.4 3.01L43.4 3.01Q40.93 2.63 38.75 2.93 36.58 3.22 33.95 4.19L33.95 4.19Q33.3 4.4 32.92 3.97 32.55 3.54 32.87 2.9L32.87 2.9Q32.98 2.31 33.46 1.85 33.95 1.4 34.27 0.86L34.27 0.86Q34.75 0 35.42-0.35 36.09-0.7 36.85-0.97L36.85-0.97Q37.33-1.13 37.81-1.37 38.3-1.61 38.89-2.04L38.89-2.04Q39.64-2.9 39.83-3.36 40.01-3.81 39.1-4.4L39.1-4.4Q37.76-5.75 37.36-7.06 36.95-8.38 37.6-9.78L37.6-9.78Q38.35-12.46 38.99-14.99 39.64-17.51 39.75-20.2L39.75-20.2Q38.62-21.7 38.43-22.75 38.24-23.79 38.67-25.24L38.67-25.24Q39.16-27.5 39.61-29.51 40.07-31.53 40.18-33.84L40.18-33.84Q39.91-34.21 39.72-34.62 39.53-35.02 39.75-35.34L39.75-35.34ZM23.2-36.52L23.2-36.52Q23.96-37.44 24.55-37.33 25.14-37.22 26-36.95L26-36.95Q26.69-36.95 27.39-36.68 28.09-36.42 28.9-35.77L28.9-35.77Q29.59-34.48 29.33-33.33 29.06-32.17 27.5-31.15L27.5-31.15Q25.83-30.35 24.38-29.73 22.93-29.11 21.27-28.25L21.27-28.25Q20.68-27.98 20.6-27.77 20.52-27.55 21.05-27.29L21.05-27.29Q21.43-27.12 21.73-26.8 22.02-26.48 22.45-26.43L22.45-26.43Q23.53-26.75 24.87-27.45 26.21-28.14 27.39-29.11L27.39-29.11Q27.93-29.86 28.87-29.97 29.81-30.08 30.62-30.08L30.62-30.08Q31.85-29.92 32.15-29.14 32.44-28.36 31.9-27.29L31.9-27.29Q30.94-26.1 29.78-25.08 28.63-24.06 27.39-23.1L27.39-23.1Q26.32-22.29 26.56-21.38 26.8-20.46 28.04-20.52L28.04-20.52Q28.95-21.16 30.45-20.73 31.96-20.3 32.98-19.66L32.98-19.66Q33.41-19.39 33.38-19.09 33.35-18.8 32.87-18.48L32.87-18.48Q31.31-17.83 29.89-17.16 28.47-16.49 26.86-15.58L26.86-15.58Q25.78-15.31 25.7-14.61 25.62-13.91 25.67-13.11L25.67-13.11Q25.62-12.62 25.83-12.62 26.05-12.62 26.43-12.78L26.43-12.78Q26.91-13.21 27.31-13.51 27.71-13.8 28.14-14.07L28.14-14.07Q28.74-14.29 28.95-14.1 29.17-13.91 29.11-13.43L29.11-13.43Q28.57-12.19 27.39-11.01 26.21-9.83 25.24-8.38L25.24-8.38Q25.03-5.8 25.11-3.25 25.19-0.7 25.14 2.26L25.14 2.26Q25.03 2.9 24.57 3.25 24.12 3.6 23.42 3.01L23.42 3.01 20.73 0.11Q20.36-0.64 20.28-1.34 20.2-2.04 19.98-2.79L19.98-2.79Q19.82-3.22 19.55-3.41 19.28-3.6 18.8-3.33L18.8-3.33Q17.62-2.47 16.7-1.75 15.79-1.02 14.82 0.32L14.82 0.32Q14.23 0.97 13.72 0.83 13.21 0.7 12.89 0L12.89 0Q11.76-1.77 11.79-3.38 11.82-5 12.89-6.77L12.89-6.77 15.68-9.88Q15.84-10.31 15.84-10.5 15.84-10.69 15.47-10.63L15.47-10.63Q14.07-10.1 12.89-9.8 11.71-9.51 10.42-9.13L10.42-9.13Q8.65-8.97 7.76-10.04 6.88-11.12 7.3-12.78L7.3-12.78Q7.2-13.48 7.39-13.86 7.57-14.23 8.27-14.72L8.27-14.72Q8.92-15.25 9.56-15.36 10.21-15.47 10.53-14.5L10.53-14.5Q10.69-14.02 11.12-13.91 11.55-13.8 11.92-13.86L11.92-13.86Q12.94-14.13 13.91-14.5 14.88-14.88 16.01-15.15L16.01-15.15Q16.87-15.58 17.99-16.11 19.12-16.65 19.77-17.4L19.77-17.4Q19.17-17.19 18.1-16.7 17.03-16.22 17.83-17.29L17.83-17.29Q19.44-18.64 20.54-19.69 21.65-20.73 21.48-22.45L21.48-22.45Q21.05-22.93 20.76-23.34 20.46-23.74 20.41-24.17L20.41-24.17 20.2-26.43Q20.09-27.12 19.74-27.42 19.39-27.71 18.8-27.29L18.8-27.29Q17.99-26.86 17.11-27.07 16.22-27.29 15.47-27.82L15.47-27.82Q14.88-28.68 15.17-29.94 15.47-31.21 16.54-32.01L16.54-32.01Q17.29-32.44 17.97-32.66 18.64-32.87 19.34-33.3L19.34-33.3Q20.36-34 21.43-34.78 22.5-35.56 23.2-36.52ZM74.23-15.15L74.23-15.15Q73.75-14.88 73.61-14.39 73.48-13.91 73.48-13.43L73.48-13.43Q74.12-12.51 74.42-13.59 74.71-14.66 74.23-15.15ZM68.96-19.01L68.64-19.55Q68.54-19.82 68.35-19.69 68.16-19.55 68.11-19.34L68.11-19.34Q67.08-17.56 66.9-16.09 66.71-14.61 66.71-12.78L66.71-12.78Q66.71-12.68 66.79-12.57 66.87-12.46 67.03-12.46L67.03-12.46Q68.75-13.54 68.78-15.52 68.8-17.51 68.96-19.01L68.96-19.01ZM75.41-23.85L75.41-23.85Q75.25-23.85 75.09-23.77 74.93-23.69 74.87-23.53L74.87-23.53Q74.34-22.5 74.2-21.54 74.07-20.57 73.91-19.44L73.91-19.44Q73.91-19.01 74.07-18.88 74.23-18.75 74.77-19.12L74.77-19.12Q75.2-20.2 75.41-21.51 75.63-22.83 75.41-23.85ZM77.77-23.31L77.77-23.31Q78.1-23.58 78.18-24.25 78.26-24.92 77.88-25.14L77.88-25.14Q77.4-25.08 77.42-24.36 77.45-23.63 77.77-23.31ZM72.83-36.09L72.83-36.09Q73.42-36.63 74.28-36.6 75.14-36.58 75.73-36.09L75.73-36.09Q76.38-35.5 76.51-34.56 76.65-33.62 76.48-32.87L76.48-32.87Q76-31.53 76.32-30.8 76.65-30.08 77.77-28.57L77.77-28.57Q78.26-28.25 78.74-28.36 79.22-28.47 79.81-28.36L79.81-28.36Q80.94-28.14 81.53-27.63 82.12-27.12 81.96-25.78L81.96-25.78Q81.48-24.06 80.57-22.45 79.65-20.84 77.88-19.01L77.88-19.01Q76.48-17.94 77.05-16.73 77.61-15.52 78.85-15.04L78.85-15.04Q79.76-14.93 79.81-14.21 79.87-13.48 79.28-12.78L79.28-12.78Q77.67-11.6 76.11-11.01 74.55-10.42 73.05-9.13L73.05-9.13Q72.67-8 72.56-6.93 72.46-5.85 72.4-4.62L72.4-4.62Q72.35-3.71 72.78-3.76 73.21-3.81 73.91-4.08L73.91-4.08Q75.46-5 75.79-5.75 76.11-6.5 75.09-7.95L75.09-7.95Q74.5-8.97 74.87-9.32 75.25-9.67 76.16-9.67L76.16-9.67Q76.48-9.67 76.89-9.53 77.29-9.4 77.77-9.13L77.77-9.13Q78.63-8.92 79.41-8.67 80.19-8.43 81.21-7.73L81.21-7.73Q82.39-6.98 82.5-6.23 82.61-5.48 82.29-4.4L82.29-4.4Q82.07-3.87 82.34-3.54 82.61-3.22 83.14-3.33L83.14-3.33Q84.65-3.81 86.04-4.14 87.44-4.46 88.95-5.59L88.95-5.59Q90.77-7.95 90.93-11.09 91.09-14.23 90.13-16.87L90.13-16.87Q89.75-17.67 89.24-17.81 88.73-17.94 88.09-18.05L88.09-18.05Q86.53-18.05 85.37-18.37 84.22-18.69 82.71-19.55L82.71-19.55Q81.91-20.2 81.94-20.65 81.96-21.11 82.82-21.38L82.82-21.38Q83.74-21.81 84.54-22.05 85.35-22.29 86.37-22.99L86.37-22.99Q87.71-23.36 88.86-23.61 90.02-23.85 91.42-24.49L91.42-24.49Q92.49-24.92 93.4-25.27 94.32-25.62 95.5-26.1L95.5-26.1Q97-25.78 98.32-25.62 99.63-25.46 101.3-25.35L101.3-25.35Q102.27-25.19 102.53-24.36 102.8-23.53 101.84-22.99L101.84-22.99Q99.63-21.38 97.75-20.65 95.87-19.93 93.56-19.23L93.56-19.23Q92.65-18.8 92.44-18.23 92.22-17.67 93.03-16.97L93.03-16.97Q96.09-14.5 96.17-12.06 96.25-9.61 95.07-6.77L95.07-6.77Q94.69-6.23 94.93-5.88 95.18-5.53 95.82-5.48L95.82-5.48Q98.08-5.69 99.85-5.77 101.62-5.85 104.09-4.4L104.09-4.4Q105.06-3.71 105.38-2.69 105.7-1.67 104.63-1.18L104.63-1.18Q104.15-0.86 103.82-0.62 103.5-0.38 103.02-0.32L103.02-0.32Q101.14-0.32 99.53-0.27 97.92-0.21 96.04 0L96.04 0Q94.96 0.27 94.05 0.32 93.13 0.38 92.17 0.54L92.17 0.54Q89.7 1.13 87.76 1.45 85.83 1.77 83.47 1.83L83.47 1.83Q82.55 1.93 81.51 1.58 80.46 1.24 79.6 0.75L79.6 0.75Q78.2-0.54 78.28-1.29 78.36-2.04 79.28-3.33L79.28-3.33Q79.71-3.97 79.47-4.35 79.22-4.73 78.53-4.4L78.53-4.4Q74.71-1.67 71.14 0.08 67.57 1.83 63.7 3.54L63.7 3.54Q62.68 3.71 62.01 3.03 61.34 2.36 61.02 1.4L61.02 1.4Q61.02 0.27 61.77-0.08 62.52-0.43 63.49-0.21L63.49-0.21Q64.18 0.48 65.26 0.08 66.33-0.32 67.14-0.64L67.14-0.64Q67.73-0.91 67.78-1.26 67.84-1.61 67.78-2.04L67.78-2.04Q68.11-2.69 68.27-3.14 68.43-3.6 68.43-4.08L68.43-4.08Q68.43-4.78 68.43-5.59 68.43-6.39 68-6.88L68-6.88Q67.73-7.14 67.25-6.9 66.76-6.66 66.49-6.45L66.49-6.45Q65.42-6.12 64.88-6.34 64.35-6.55 64.13-7.73L64.13-7.73Q63.97-8.65 64.05-9.43 64.13-10.21 64.02-11.06L64.02-11.06Q63.33-11.92 62.49-12.22 61.66-12.51 60.91-12.89L60.91-12.89Q59.51-15.63 59.54-18.29 59.57-20.95 60.26-23.53L60.26-23.53Q61.12-25.62 62.52-25.49 63.92-25.35 65.42-23.96L65.42-23.96Q65.69-23.42 65.98-23.44 66.28-23.47 66.71-23.74L66.71-23.74Q67.46-24.22 68.35-24.79 69.23-25.35 69.61-26.21L69.61-26.21Q69.93-28.52 70.42-30.51 70.9-32.5 71.97-34.8L71.97-34.8Q72.08-35.18 72.29-35.5 72.51-35.83 72.83-36.09Z"
                  transform="translate(0, 30)"></path>
              </g>
            </svg>
          </div>
        </div>
      </template>

      <template v-if="isSidebarMixedNav">
        <slot name="mixed-menu"></slot>
      </template>
      <template v-else>
        <slot name="menu"></slot>
      </template>

      <template #extra>
        <slot name="side-extra"></slot>
      </template>
      <template #extra-title>
        <slot name="side-extra-title"></slot>
      </template>
    </LayoutSidebar>

    <div ref="contentRef" class="flex flex-1 flex-col overflow-hidden transition-all duration-300 ease-in">
      <div :class="[
        {
          'shadow-[0_16px_24px_hsl(var(--background))]': scrollY > 20,
        },
        SCROLL_FIXED_CLASS,
      ]" :style="headerWrapperStyle" class="overflow-hidden transition-all duration-200">
        <LayoutHeader v-if="headerVisible" :full-width="!isSideMode" :height="headerHeight" :is-mobile="isMobile"
          :show="!isFullContent && !headerHidden" :sidebar-width="sidebarWidth" :theme="headerTheme"
          :width="mainStyle.width" :z-index="headerZIndex">
          <template v-if="showHeaderLogo" #logo>
            <slot name="logo"></slot>
          </template>

          <template #toggle-button>
            <VbenIconButton v-if="showHeaderToggleButton" class="my-0 mr-1 rounded-md" @click="handleHeaderToggle">
              <Menu class="size-4" />
            </VbenIconButton>
          </template>
          <slot name="header"></slot>
        </LayoutHeader>


      </div>

      <!-- </div> -->
      <LayoutContent :id="idMainContent" :content-compact="contentCompact" :content-compact-width="contentCompactWidth"
        :padding="contentPadding" :padding-bottom="contentPaddingBottom" :padding-left="contentPaddingLeft"
        :padding-right="contentPaddingRight" :padding-top="contentPaddingTop" :style="contentStyle"
        class="transition-[margin-top] duration-200">
        <slot name="content"></slot>

        <template #overlay>
          <slot name="content-overlay"></slot>
        </template>
      </LayoutContent>

      <LayoutFooter v-if="footerEnable" :fixed="footerFixed" :height="footerHeight" :show="!isFullContent"
        :width="footerWidth" :z-index="zIndex">
        <slot name="footer"></slot>
      </LayoutFooter>
    </div>
    <slot name="extra"></slot>
    <div v-if="maskVisible" :style="maskStyle"
      class="bg-overlay fixed left-0 top-0 h-full w-full transition-[background-color] duration-200"
      @click="handleClickMask"></div>
  </div>
</template>

<style scoped lang="postcss">
.sys-title {
  @apply flex h-full items-center gap-2 px-3;
  justify-content: flex-start;
  overflow: hidden;

  &.compact {
    min-width: auto;
    .title-name { display: none; }
  }
}

.title-img {
  @apply flex-shrink-0;
  width: 36px;
  min-width: 36px;
}

.title-name {
  @apply flex-shrink-0;
  width: 84px;
  min-width: 84px;

  svg {
    @apply text-foreground;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>
