import { ref, onBeforeMount, watch } from 'vue';
import { useRoute } from 'vue-router';
import { useTaskStore } from '#/store/task';
import { storeToRefs } from 'pinia';
import usePreviewTypes from './use-preview-types';
import { decodeURIData } from '#/utils/transform';
import usePlatformStore from '#/store/platform';
const { taskItem, taskFileExtension } = storeToRefs(useTaskStore());
const { biz, queue } = storeToRefs(usePlatformStore());
// interface PreviewData {
//   keywords?: string[];
//   token?: string;
//   url?: string;
//   extra_url?: string;
//   ocr_url?: string;
//   fname?: string;
//   url_bucket?: string;
// }
// 预览数据,将多渠道数据进行清洗合并，算是一个适配层函数
const usePreviewData = () => {
  const route = useRoute();
  console.log('taskItemB', taskItem.value);
  // 数据是否获取成功
  const ok = ref<boolean>(false);
  // const previewData = ref<PreviewData>({});
  const fileID = ref<string | undefined>(undefined);
  const extraID = ref<string | undefined>(undefined);
  const scene = ref<string | undefined>(undefined);
  const tag = ref<number>(0);
  const ext = ref<string>('');
  const url = ref<string>('');
  const { viewTypes, configViewTypes } = usePreviewTypes(
    taskItem,
    biz,
    queue,
    taskFileExtension,
  );
  const viewType = ref<number>(1);

  const initLocalData = () => {
    const route_fileinx = route.query.fileinx as string;
    const route_fileid = route.query.fileid as string;
    // 当存在路由参数时，说明是file.vue跳转过来的，优先使用路由参数
    if (route_fileinx || route_fileid) {
      fileID.value = route_fileinx || route_fileid;
      extraID.value = route.query.extra_id as string;
      scene.value = route.query.scene as string;
      tag.value = Number(route.query.tag || 0);
      ext.value = route.query.ext as string;
      url.value = route.query.url
        ? decodeURIData(route.query.url as string)
        : '';
      viewTypes.value = route.query.view_types
        ? decodeURIData(route.query.view_types as string)
        : [];
      biz.value = route.query.biz as string;
      viewType.value = viewTypes.value[0] || 1;
      ok.value = true;
      return;
    }
    // 使用taskItem
    if (taskItem.value) {
      console.log('taskItem', taskItem.value);
      fileID.value = taskItem.value.fileinx;
      extraID.value = taskItem.value.extra_id;
      scene.value = taskItem.value.scene;
      tag.value = taskItem.value.task_tag;
      ext.value = taskItem.value.fname.split('.').pop()?.toLowerCase() || '';
      url.value = taskItem.value.preview_url || '';
      configViewTypes();
      viewType.value = viewTypes.value[0] || 1;
      ok.value = true;
    }
  };

  onBeforeMount(() => {
    initLocalData();
  });
  watch(taskItem, (newVal) => {
    if (newVal) {
      initLocalData();
    }
  });
  return {
    extraID,
    scene,
    fileID,
    tag,
    ext,
    url,
    ok,
    viewTypes,
    viewType,
    biz,
    configViewTypes,
  };
};

export default usePreviewData;
