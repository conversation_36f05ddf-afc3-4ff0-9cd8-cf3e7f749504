<script setup lang="ts">
import { ref } from 'vue';
import { CaretDownOutlined } from '@ant-design/icons-vue';
import { updateAiRecord, updateModelType } from '#/api/ai-mark';
import { useApi<PERSON>andler } from '#/composables/common/use-api-handler';

const { handleApiCall } = useApiHandler();
const props = defineProps({
  aimarkLabelOptions: {
    type: Array<any>,
    required: true,
  },
  scope: {
    type: Object,
    required: true,
  },
  updateType: {
    type: String,
    default: '',
  },
  iscv: {
    type: Boolean,
    default: false,
  },
  callBack: Function,
});

const loading = ref(false);

const handleMenuClick = ({ key }: any) => {
  update(props.scope, key);
};

const update = async (item: any, type: any) => {
  loading.value = true;

  if (props.updateType === 'ai_text_marking') {
    await handleApiCall(
      updateModelType,
      {
        sample_id: item.sample_id,
        sample_type: type,
      },
      {
        onSuccess: () => props.callBack?.(),
      },
    );
  } else {
    const params: any = {
      id: item.id,
      type: type,
    };
    if (props.iscv) {
      params.entry = 'cv_mark_log';
    }
    await handleApiCall(updateAiRecord, params, {
      onSuccess: () => props.callBack?.(),
    });
  }
  loading.value = false;
};
</script>
<template>
  <div class="update-btn-wrap">
    <a-dropdown
      :trigger="['click']"
      :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
    >
      <a-button type="default" size="small" :loading="loading">
        操作
        <CaretDownOutlined />
      </a-button>
      <template #overlay>
        <a-menu @click="handleMenuClick">
          <a-menu-item
            v-for="item in aimarkLabelOptions"
            :key="item.value"
            :name="item.value"
          >
            {{ item.label }}
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
  </div>
</template>
