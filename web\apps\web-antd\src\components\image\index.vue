<script lang="ts" setup>
import type { TransformEvent } from '#/composables/ui/use-image-transformer';

import { computed, nextTick, onMounted, ref, watch } from 'vue';

import { httpsCheck } from '@vben/utils';

import { message } from 'ant-design-vue';

import { applyRemoteTask } from '#/api/download-cloud';
import { useImageTransformer } from '#/composables/ui/use-image-transformer';

import ImageCore from './img-core.vue';

const props = withDefaults(
  defineProps<{
    debug?: boolean;
    disabledDownload?: boolean;
    fileItem?: any;
    imageItem?: any;
    imgHeightClass?: string; // h-[300px]
    // 是否显示hover图片
    showHoverImage?: boolean;
    src: string;
    // 标签背景色，可以是单个颜色或颜色数组，与tagText一一对应
    tagColor?: string | string[];
    // 左上角标签文本，可以是单个字符串或字符串数组
    tagText?: string | string[];
    tooltipHeight?: number;
    tooltipPlacement?: string;
    // tooltip图片的最大宽高
    tooltipWidth?: number;
  }>(),
  {
    debug: false,
    fileItem: undefined,
    imageItem: undefined,
    disabledDownload: false,
    tooltipWidth: 200,
    tooltipHeight: 150,
    tooltipPlacement: 'topRight',
    showHoverImage: false,
    tagText: '预览',
    tagColor: '#1890ff',
    imgHeightClass: 'h-full',
  },
);

const emit = defineEmits<{
  (e: 'afterTransform', payload: TransformEvent): void;
  (e: 'error', event: Event): void;
  (e: 'load', event: Event): void;
}>();

// 使用抽取出的composable
const {
  // 状态
  showViewer,
  showSmallBox,
  transform,
  isDragging,

  // 尺寸和位置
  currentWidth,
  currentHeight,
  containerWidth,
  containerHeight,
  imageWidth,
  imageHeight,
  bWidth,
  bHeight,

  // 计算属性
  birdStyle,
  floatStyle,

  // DOM引用
  imgContainerRef,
  floatBoxRef,
  imgElementRef,
  innerBoxRef,

  // 方法
  onPreviewClick,
  onCloseViewer,
  updateTransform,
  updateContainerSize,
  closeSmallBox,
  onBirdImageLoad,
  startDrag,
  initialize,
  getInnerSize,
} = useImageTransformer((e, payload) => emit(e, payload));

const hasError = ref<boolean>(false);
// 图片加载事件处理
function handleImageLoad(e: Event) {
  emit('load', e);
}
function handleError(e: Event) {
  hasError.value = true;
  emit('error', e);
}
onMounted(() => {
  initialize();
});

const imageSrc = computed(() => httpsCheck(props.src));
// 监听transform变化时更新鸟瞰图
watch(
  () => [
    transform.offsetX,
    transform.offsetY,
    transform.deg,
    transform.flipX,
    transform.flipY,
  ],
  () => {
    if (showSmallBox.value) {
      nextTick(() => {
        getInnerSize();
      });
    }
  },
);

/**
 * 处理标签数据，确保标签文本和颜色都是数组格式
 * @returns {{tags: string[], colors: string[]}} 标准化后的标签数据
 *
 * @example
 * // 输入为单个字符串时
 * // props.tagText = '预览', props.tagColor = '#1890ff'
 * // 返回 {tags: ['预览'], colors: ['#1890ff']}
 *
 * // 输入为数组时
 * // props.tagText = ['预览', '新品'], props.tagColor = ['#1890ff', '#f5222d']
 * // 返回 {tags: ['预览', '新品'], colors: ['#1890ff', '#f5222d']}
 */
const normalizedTags = computed(() => {
  // 将标签文本统一转为数组
  const tags = Array.isArray(props.tagText) ? props.tagText : [props.tagText];

  // 将标签颜色统一转为数组
  let colors: string[] = [];
  if (Array.isArray(props.tagColor)) {
    // 如果颜色是数组，则使用对应位置的颜色
    colors = props.tagColor;
    // 如果颜色数组长度不够，用最后一个颜色填充
    if (colors.length < tags.length) {
      const lastColor = colors[colors.length - 1] || '#1890ff';
      colors = [
        ...colors,
        ...Array.from<string>({ length: tags.length - colors.length }).fill(
          lastColor,
        ),
      ];
    }
  } else {
    // 如果颜色是单个值，则所有标签使用相同颜色
    colors = Array.from<string>({ length: tags.length }).fill(props.tagColor);
  }

  return { tags, colors };
});
const handleDownloadCloude = async () => {
  const o = {
    fileid: props.fileItem.fileinx,
    extra_id: props.fileItem.extra_id,
    scene: props.fileItem.scene,
    biz: props.fileItem.biz,
    file_type: 'INNER',
    fname: props.imageItem.key,
    url: props.src,
  };
  const resp = await applyRemoteTask(o);
  console.log('resp', resp);
  if (resp.result) {
    message.info('下载中，请稍后在下载列表中查看~');
  }
};
</script>

<template>
  <div>
    <!-- 小图，点击后打开大图 -->
    <div v-if="!hasError" class="h-full w-full">
      <a-tooltip
        v-if="props.showHoverImage"
        :placement="tooltipPlacement"
        overlay-class-name="no-padding-tooltip"
      >
        <template #title>
          <div class="relative h-full w-full overflow-hidden">
            <img
              :src="imageSrc"
              :style="{
                width: `${props.tooltipWidth}px`,
                height: `${props.tooltipHeight}px`,
                margin: '0 auto',
                display: 'block',
              }"
              alt="preview"
            />
            <!-- 左上角标签区域 -->
            <div class="absolute left-0 top-0 flex flex-wrap">
              <div
                v-for="(tag, index) in normalizedTags.tags"
                :key="index"
                :style="{ backgroundColor: normalizedTags.colors[index] }"
                class="mb-1 mr-1 rounded px-2 py-0.5 text-xs text-white"
              >
                {{ tag }}
              </div>
            </div>
          </div>
        </template>
        <img
          :src="imageSrc"
          alt="preview"
          class="h-full w-full cursor-pointer select-none [-webkit-user-drag:none]"
          @click="onPreviewClick"
          @error="handleError"
          @load="handleImageLoad"
        />
      </a-tooltip>

      <!-- 不显示悬停图片时的普通图片 -->
      <img
        v-else
        :class="imgHeightClass"
        :src="imageSrc"
        alt="preview"
        class="w-full cursor-pointer select-none [-webkit-user-drag:none]"
        @click="onPreviewClick"
        @error="handleError"
        @load="handleImageLoad"
      />
    </div>

    <div
      v-if="hasError"
      class="flex h-full items-center justify-center rounded border bg-white text-black-500 font-bold"
    >
      加载失败<span v-if="!disabledDownload">
        ，请<a-button class="px-0" type="link" @click="handleDownloadCloude">
          下载查看
        </a-button>
        !
      </span>
      <span v-else> ！ </span>
    </div>
    <!-- 子组件大图查看器 -->
    <image-core
      v-if="showViewer"
      :img-src="imageSrc"
      :offset-x="transform.offsetX"
      :offset-y="transform.offsetY"
      @after-transform="updateTransform"
      @close="onCloseViewer"
      @container-rect-change="updateContainerSize"
    />

    <!-- 鸟瞰图小窗 -->
    <div
      v-if="showViewer && showSmallBox"
      class="fixed bottom-5 right-2.5 z-[9999] flex h-[180px] w-[180px] touch-none flex-col overflow-hidden rounded-lg border border-gray-300/50 bg-white/90 shadow-md"
    >
      <div
        class="flex h-6 w-full items-center justify-between bg-blue-500 px-2 text-xs text-white"
      >
        <span>鸟瞰图</span>
        <button
          class="cursor-pointer border-none bg-transparent p-0 px-1 text-sm leading-none text-white"
          @click="closeSmallBox"
        >
          ×
        </button>
      </div>
      <div
        ref="imgContainerRef"
        class="relative flex flex-1 touch-none items-center justify-center overflow-hidden"
      >
        <!-- 图片本身，应用旋转/翻转 -->
        <img
          ref="imgElementRef"
          :src="imageSrc"
          :style="birdStyle"
          alt="bird"
          class="relative max-h-full max-w-full select-none object-contain [-webkit-user-drag:none]"
          @load="onBirdImageLoad"
        />

        <!-- 可视区域框，可拖动 -->
        <div
          ref="floatBoxRef"
          :style="floatStyle"
          class="pointer-events-auto absolute z-[1] box-content transform-gpu cursor-grab touch-none select-none border-[300px] border-amber-500/50 bg-amber-500/10 [-webkit-tap-highlight-color:transparent] [will-change:transform,top,left] active:cursor-grabbing active:opacity-80"
          @mousedown.prevent="startDrag"
          @touchstart.prevent="startDrag"
        >
          <!-- 内部元素用于显示内容裁剪 -->
          <div
            ref="innerBoxRef"
            class="pointer-events-none h-full w-full bg-amber-500/20"
          ></div>
        </div>
      </div>

      <!-- 调试信息面板 -->
      <div
        v-if="props.debug"
        class="pointer-events-none absolute bottom-0 left-0 right-0 z-[101] bg-black/50 p-1 text-[9px] text-white"
      >
        <div>原图: {{ imageWidth }}x{{ imageHeight }}</div>
        <div>鸟瞰图: {{ bWidth }}x{{ bHeight }}</div>
        <div>
          大图: {{ Math.round(currentWidth) }}x{{ Math.round(currentHeight) }}
        </div>
        <div>
          偏移: {{ Math.round(transform.offsetX) }},{{
            Math.round(transform.offsetY)
          }}
        </div>
        <div>
          大图容器: {{ Math.round(containerWidth) }},{{
            Math.round(containerHeight)
          }}
        </div>
        <div>
          旋转: {{ transform.deg }}° | 缩放:
          {{ (currentWidth / imageWidth).toFixed(2) }}
        </div>
        <div>拖动: {{ isDragging ? '是' : '否' }}</div>
        <div>状态: {{ showSmallBox ? '显示' : '隐藏' }}</div>
      </div>
    </div>
  </div>
</template>

<style>
/* 移除tooltip内边距 */
.no-padding-tooltip .ant-tooltip-inner {
  padding: 0;
  overflow: hidden;
}
</style>
