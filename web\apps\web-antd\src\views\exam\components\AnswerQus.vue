<script setup>
import { ref, reactive, computed, watch } from 'vue';
import ImgView from '#/components/image/index.vue';
import AutoWidthInput from './AutoWidthInput.vue';

const emits = defineEmits(['update:myAnswer']);
const props = defineProps({
  question: {
    type: Object,
  },
  myAnswer: {
    type: String,
  },
  myScore: {
    type: Number,
  },
  order: {
    type: Number,
  },
  isDisabled: {
    type: Boolean,
    default: false,
  },
  paperInfo: {
    type: Object,
  },
  full_mark: {
    type: Number,
    default: null,
  },
  isSubmit: {
    type: Boolean,
    default: false,
  },
});

const title = reactive({
  msg: '',
  images: [],
});
const fullScore = ref('');
const radioAnswer = ref('');
const judgeAnswer = ref('');

const judgeChoice = [
  {
    value: 'true',
    label: '正确',
  },
  {
    value: 'false',
    label: '错误',
  },
];
const mutipleAnswers = ref([]);
const blankAnswers = ref([]);
const shortAnswer = ref('');
const correctAnswer = ref('');

const correctAnswerDescription = computed(() => {
  if (props.question.question_type === 1)
    return correctAnswer.value?.toUpperCase();
  else if (props.question.question_type === 2) {
    return correctAnswer.value?.toUpperCase();
  } else if (props.question.question_type === 3) {
    if (correctAnswer.value === 'true') {
      return '正确';
    } else {
      return '错误';
    }
  } else {
    return correctAnswer.value;
  }
});

const isAnswered = computed(() => {
  if (props.question.question_type === 1 && !radioAnswer.value) return false;
  if (props.question.question_type === 3 && !judgeAnswer.value) return false;
  if (props.question.question_type === 2) {
    const firstNullAnswer = mutipleAnswers.value.find(
      (element) => element === '',
    );
    if (firstNullAnswer === '') return false;
  }
  if (props.question.question_type === 4) {
    const firstNullBlank = blankAnswers.value.find((element) => element === '');
    if (firstNullBlank === '') return false;
  }
  if (props.question.question_type === 5 && !shortAnswer.value) return false;
  return true;
});

watch(radioAnswer, () => {
  emits('update:myAnswer', radioAnswer.value);
});
watch(judgeAnswer, () => {
  emits('update:myAnswer', judgeAnswer.value);
});
watch(
  mutipleAnswers,
  () => {
    const sortedAnswers = [...mutipleAnswers.value].sort();
    const string = sortedAnswers.join('');
    emits('update:myAnswer', string);
  },
  {
    deep: true,
  },
);
watch(
  blankAnswers,
  () => {
    const string = blankAnswers.value.join(',');
    emits('update:myAnswer', string);
  },
  { deep: true },
);
watch(shortAnswer, () => {
  emits('update:myAnswer', shortAnswer.value);
});
watch(
  () => props.question,
  (newVal) => {
    dataInit();
  },
  {
    deep: true,
  },
);
dataInit();

function dataInit() {
  const { question, myAnswer } = props;
  const { question_type } = question;

  correctAnswer.value = question.answer;

  title.msg =
    question_type === 4
      ? question.title.msg.replace(/_ /g, '___ ')
      : question.title.msg;

  title.images = question.title.images;

  switch (question_type) {
    case 1:
      radioAnswer.value = myAnswer;
      break;

    case 2:
      mutipleAnswers.value = myAnswer.split('');
      break;

    case 3:
      judgeAnswer.value = myAnswer;
      break;

    case 4:
      const blankCount = (question.title.msg.match(/_ /g) || []).length;
      blankAnswers.value = Array(blankCount).fill('');
      if (myAnswer) {
        myAnswer.split(',').forEach((item, index) => {
          blankAnswers.value[index] = item;
        });
      }
      break;

    case 5:
      shortAnswer.value = myAnswer;
      break;

    default:
      console.warn(`未知的题型: ${question_type}`);
  }
}
</script>
<template>
  <div class="mx-auto mb-6 mt-6 w-[70%] text-base">
    <div class="mb-4">
      <div
        class="line-height-[20px] text-wrap break-words text-[16px] leading-7"
      >
        <span
          :class="{
            'bg-amber-200': isSubmit && !isAnswered,
            'leading-5': true,
          }"
        >
          {{ order }}.
        </span>

        <span
          v-if="question.question_type === 2"
          class="mr-1.25 text-sm text-gray-500"
          >[多选]</span
        >
        {{ title.msg }}
        <span class="text-sm text-blue-500">({{ full_mark }}分)</span>
      </div>
      <div v-if="title.images && title.images.length" class="mt-1.25 flex">
        <div
          v-for="(item, index) in title.images"
          class="relative mr-2.5 h-[150px] w-[150px]"
        >
          <img-view class="z-1 h-full w-full" :src="item"> </img-view>
        </div>
      </div>
    </div>

    <a-radio-group
      :disabled="isDisabled"
      v-if="question.question_type === 1"
      v-model:value="radioAnswer"
      class="flex flex-col"
    >
      <div
        v-for="(value, key, index) in question.content.choice"
        :key="key"
        class="mb-4"
      >
        <a-radio
          :value="key"
          class="line-height-[20px] flex w-full items-center whitespace-pre-wrap"
          >{{ String.fromCharCode(64 + index + 1) + '. ' + value.msg }}</a-radio
        >
        <div
          v-if="value.images && value.images.length"
          class="mt-1.25 ml-6 flex"
        >
          <div
            v-for="(item, imgIndex) in value.images"
            class="relative mr-2.5 h-[120px] w-[120px]"
          >
            <img-view class="z-1 h-full w-full" :key="index" :src="item">
            </img-view>
          </div>
        </div>
      </div>
    </a-radio-group>
    <a-radio-group
      :disabled="isDisabled"
      v-if="question.question_type === 3"
      v-model:value="judgeAnswer"
      class="flex flex-col"
      :options="judgeChoice"
    >
    </a-radio-group>
    <a-checkbox-group
      :disabled="isDisabled"
      v-else-if="question.question_type === 2"
      v-model:value="mutipleAnswers"
      class="flex flex-col"
    >
      <div
        class="mb-4"
        v-for="(value, key, index) in question.content.choice"
        :key="key"
      >
        <a-checkbox
          :value="key"
          class="line-height-[20px] flex w-full items-center whitespace-pre-wrap"
          >{{
            String.fromCharCode(64 + index + 1) + '. ' + value.msg
          }}</a-checkbox
        >
        <div
          v-if="value.images && value.images.length"
          class="mt-1.25 ml-6 flex"
        >
          <div
            v-for="(item, imgIndex) in value.images"
            class="relative mr-2.5 h-[120px] w-[120px]"
          >
            <img-view class="z-1 h-full w-full" :key="index" :src="item">
            </img-view>
          </div>
        </div>
      </div>
    </a-checkbox-group>

    <div v-else-if="question.question_type === 4">
      <span class="text-sm" v-if="isDisabled">我的答案：</span>

      <auto-width-input
        v-for="(item, index) in blankAnswers"
        :key="index"
        v-model:value="blankAnswers[index]"
        :readonly="isDisabled"
      >
      </auto-width-input>
    </div>
    <div v-else-if="question.question_type === 5">
      <div class="mb-1.5 text-sm" v-if="isDisabled">我的答案：</div>

      <a-textarea
        class="mb-4"
        type="textarea"
        placeholder="请输入答案"
        v-model:value="shortAnswer"
        :autoSize="{ minRows: 2, maxRows: 10 }"
        :readOnly="isDisabled"
      />
    </div>

    <div v-if="isDisabled" class="break-words text-sm text-blue-500">
      <div class="mb-2.5" v-if="myScore === -1">得分：0</div>
      <div class="mb-2.5" v-else>得分：{{ myScore }}</div>
      <div class="mb-2.5">
        <span>正确答案：</span>
        <span>{{ correctAnswerDescription }}</span>
      </div>
      <div>
        试题剖析：
        <span class="text-gray-500" v-if="[1, 2].includes(question.question_type) && question.content.choice">
          <template v-for="(value, key, index) in question.content.choice" :key="key">
            <template v-if="value.analyse">
              {{ String.fromCharCode(64 + index + 1) + '.' + value.analyse }}
            </template>
          </template>
          <template v-if="question.content.analyse">
            {{ question.content.analyse }}
          </template>
        </span>
        <span class="text-gray-500" v-else>
          {{ question.content.analyse }}
        </span>
      </div>
    </div>
  </div>
</template>
