{"name": "@vben/web-antd", "version": "5.5.1", "homepage": "https://vben.pro", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "apps/web-antd"}, "license": "MIT", "author": {"name": "vben", "email": "<EMAIL>", "url": "https://github.com/anncwb"}, "type": "module", "scripts": {"build": "pnpm vite build --mode production", "build:test": "pnpm vite build --mode development", "build:analyze": "pnpm vite build --mode analyze", "dev": "pnpm vite --mode development", "preview": "vite preview", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>"}, "imports": {"#/*": "./src/*"}, "exports": {".": {"types": "./src/index.ts", "default": "./src/index.ts"}}, "dependencies": {"@tanstack/vue-query": "catalog:", "@vben/access": "workspace:*", "@vben/common-ui": "workspace:*", "@vben/constants": "workspace:*", "@vben/hooks": "workspace:*", "@vben/icons": "workspace:*", "@vben/layouts": "workspace:*", "@vben/locales": "workspace:*", "@vben/plugins": "workspace:*", "@vben/preferences": "workspace:*", "@vben-core/shadcn-ui": "workspace:*", "@vben/request": "workspace:*", "@vben/stores": "workspace:*", "@vben/styles": "workspace:*", "@vben/types": "workspace:*", "@vben/utils": "workspace:*", "@vueuse/core": "catalog:", "ant-design-vue": "catalog:", "dayjs": "catalog:", "guacamole-client": "^1.5.5", "monaco-editor": "^0.52.2", "pinia": "catalog:", "qrcode": "^1.5.4", "video.js": "^8.22.0", "vue": "catalog:", "vue-router": "catalog:"}, "devDependencies": {"@types/guacamole-client": "^1.0.9", "@vitejs/plugin-basic-ssl": "^1.2.0", "vite-plugin-monaco-editor": "^1.1.0"}}