import type { RouteRecordRaw } from 'vue-router';
import { BasicLayout } from '#/layouts';
import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    path: '/bussinessInfo',
    name: 'bussinessInfo',
    component: BasicLayout,
    meta: {
      icon: 'ant-design:align-left-outlined',
      title: $t('page.bussinessInfo.title'),
    },
    children: [
      {
        path: '/bussinessInfo',
        component: () => import('#/views/bussinessInfo/index.vue'),
        meta: {
          title: $t('page.bussinessInfo.title'),
          hideInMenu: true,
        },
      },
    ],
  },
];

export default routes;