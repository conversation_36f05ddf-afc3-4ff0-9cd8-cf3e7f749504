<script lang="ts" setup>
import type { Keyword, Range } from '#/types/task';

import { computed, onBeforeUnmount, onMounted, ref, watch } from 'vue';

import * as monaco from 'monaco-editor';
import { storeToRefs } from 'pinia';

import { getExpireUrlBatch } from '#/api/preview';
import { useTextSelection } from '#/composables/ai-mark/use-text-selection';
import usePlatformStore from '#/store/platform';
import { useTaskStore } from '#/store/task';
// 生成唯一的语言ID，基于索引
const props = defineProps<{
  imageBucket?: string;
  imageKey?: string;
  imageUrl?: string; // 添加图片URL属性
  index?: number;
  keywords: Keyword[]; // 要高亮的关键字数组
  maxheight?: number;
  tag?: string;
  value: string;
}>();
const platformStore = usePlatformStore();
const taskStore = useTaskStore();
const maxheight = props.maxheight ?? 600;

// 使用计算属性生成唯一语言ID
const curMySpecialLanguage = computed(() => {
  return `KcsLanguage${props.index === undefined ? '' : `_${props.index}`}`;
});

const editorContainer = ref<HTMLElement | null>();
let editor: monaco.editor.IStandaloneCodeEditor | null = null;
let model: monaco.editor.ITextModel | null = null;
let decorations: monaco.editor.IEditorDecorationsCollection | undefined;
let hoverDisposable: monaco.IDisposable | null = null;
const height = ref('0px');
const noExpireUrl = ref('');
const { handleTextSelection, hasSelectedText, selectedText, clearSelection } =
  useTextSelection();
const { isTextMark } = storeToRefs(platformStore);

const handleMouseUp = () => {
  if (!isTextMark.value || !editorContainer.value) return;
  handleTextSelection(editorContainer.value);
};
// 在 updateDecorations 中添加验证
const validateRange = (range: Range) => {
  const maxLine = model?.getLineCount() || 1;
  if (range.start.row < 1 || range.end.row > maxLine) return false;
  const maxColumn = model?.getLineMaxColumn(range.start.row) || 1;

  return (
    range.start.row > 0 &&
    range.end.row <= maxLine &&
    range.start.column > 0 &&
    range.end.column <= maxColumn
  );
};

// 设置悬浮提供器
const setupHoverProvider = () => {
  // 清除旧的悬浮提供器
  if (hoverDisposable) {
    hoverDisposable.dispose();
    hoverDisposable = null;
  }

  // 注册新的悬浮提供器
  hoverDisposable = monaco.languages.registerHoverProvider(
    curMySpecialLanguage.value,
    {
      async provideHover(_, position) {
        // 尝试获取新的过期URL（如果需要）
        if (props.imageKey && !noExpireUrl.value) {
          try {
            const params = [
              {
                key: props.imageKey,
                bucket: props.imageBucket || '',
              },
            ];
            const res = await getExpireUrlBatch(params);

            if (res.result && res.data.items && res.data.items.length > 0) {
              noExpireUrl.value = res.data.items[0].url;
            }
          } catch (error) {
            console.error('Failed to get expire URL:', error);
          }
        }

        // 图片URL - 优先使用获取的新URL，其次使用传入的URL
        const imgUrl = noExpireUrl.value || props.imageUrl;
        if (!imgUrl) return null;

        // 检查位置是否在关键词范围内
        for (const keyword of props.keywords) {
          if (!keyword.ranges) continue;

          for (const range of keyword.ranges) {
            if (!validateRange(range)) continue;

            if (
              position.lineNumber >= range.start.row &&
              position.lineNumber <= range.end.row &&
              position.column >= range.start.column &&
              position.column <= range.end.column
            ) {
              // 在关键词范围内，显示图片
              return {
                range: new monaco.Range(
                  range.start.row,
                  range.start.column,
                  range.end.row,
                  range.end.column,
                ),
                contents: [
                  {
                    value: `![monaco-img-preview](${imgUrl})`,
                    isTrusted: true,
                  },
                ],
              };
            }
          }
        }

        return null; // 不在任何关键词范围内
      },
    },
  );
};

// 生成高亮装饰器
const updateDecorations = () => {
  // 清除旧装饰器
  if (decorations) {
    decorations.clear();
  }
  if (!model || props.keywords.length === 0) return;

  const decorators: monaco.editor.IModelDeltaDecoration[] = [];
  const highlightMap: Record<number, string> = {
    1: 'onelevel-highlight',
    201: 'exemption-highlight',
  };
  props.keywords.forEach((item) => {
    if (!item.ranges) return;
    item.ranges.forEach((range) => {
      if (validateRange(range)) {
        const className = highlightMap[item.kind] || 'otherlevel-highlight';

        decorators.push({
          range: new monaco.Range(
            range.start.row,
            range.start.column,
            range.end.row,
            range.end.column,
          ),
          options: {
            inlineClassName: className,
          },
        });
      } else {
        console.warn('Invalid range:', range);
      }
    });
  });

  // 确保编辑器存在
  if (!editor) {
    console.error('Editor is not initialized when applying decorations');
    return;
  }

  // 应用装饰
  decorations = editor.createDecorationsCollection(decorators);
};
const updateHeight = () => {
  if (!editor) return;
  height.value = `${Math.max(Math.min(editor.getContentHeight(), maxheight), 40) * 1.5}px`;
  console.log('height', height.value);
};
// 初始化编辑器
onMounted(() => {
  if (!editorContainer.value) {
    return;
  }

  // 注册语言
  monaco.languages.register({ id: curMySpecialLanguage.value });

  // 确保主题已设置
  monaco.editor.setTheme('KcsTheme');

  editor = monaco.editor.create(editorContainer.value, {
    value: props.value,
    theme: 'KcsTheme',
    readOnly: true, // 不能编辑
    automaticLayout: true,
    language: curMySpecialLanguage.value,
    lineNumbers: 'off', // 控制行号的呈现
    wordWrap: 'on',
    links: false, // 禁用链接
    scrollBeyondLastLine: false,
    contextmenu: false,
    overviewRulerLanes: 0,
    hideCursorInOverviewRuler: true,
    overviewRulerBorder: false,
    largeFileOptimizations: false, // 最大宽度限制取消
    minimap: {
      enabled: false,
    },
    scrollbar: {
      horizontal: 'hidden',
    },
    // 添加hover配置
    hover: {
      above: false, // 设置为false表示不要在上方显示
      delay: 300, // 添加一点延迟
    },
    domReadOnly: true, // 使用DOM的readonly属性
  });
  // 添加初始化完成检查
  const checkModel = () => {
    if (editor && !model) {
      model = editor.getModel();
      if (model) {
        updateHeight();
        updateDecorations();
        setupHoverProvider(); // 设置悬浮提供器
      } else {
        setTimeout(checkModel, 50); // 轮询直到 model 就绪
      }
    }
  };

  checkModel();
});

const focus = (pos: { column: number; lineNumber: number }) => {
  if (editor) {
    editor.revealPositionInCenter(pos);
  }
};

watch(
  () => props.value,
  (newValue) => {
    try {
      if (editor && model) {
        editor.setValue(newValue);
        updateHeight();
        updateDecorations();
        editor.layout();
      }
    } catch (error) {
      console.error('Value update failed:', error);
    }
  },
);

watch(
  () => [...props.keywords],
  () => {
    updateDecorations();
  },
);

// 监听图片属性变化，当变化时更新悬浮提供器
watch(
  [() => props.imageUrl, () => props.imageKey, () => props.imageBucket],
  () => {
    noExpireUrl.value = ''; // 清除之前的URL，以便重新获取
    setupHoverProvider();
  },
);

onBeforeUnmount(() => {
  if (hoverDisposable) {
    hoverDisposable.dispose();
  }
  clearSelection();
  if (editor) {
    editor.dispose();
  }
});

const handleConfirm = () => {
  taskStore.addMarkText(selectedText.value);
  clearSelection();
};
defineExpose({
  focus,
  editorContainer,
  keywords: props.keywords,
});

export type MonacoEditorInstance = {
  editorContainer: HTMLElement | null;
  focus: (pos: { column: number; lineNumber: number }) => void;
  keywords: Keyword[];
};
</script>
<template>
  <div
    ref="editorContainer"
    :style="{ height }"
    class="relative p-2"
    @mouseup="handleMouseUp"
  >
    <div v-if="tag" class="absolute left-2 top-1 z-10 text-xs text-red-500">
      {{ tag }}
    </div>
    <a-modal
      v-model:open="hasSelectedText"
      :width="500"
      title="标注"
      @cancel="clearSelection"
      @ok="handleConfirm"
    >
      <div>{{ selectedText }}</div>
    </a-modal>
  </div>
</template>
<style scoped lang="scss">
:deep(.monaco-editor) {
  .onelevel-highlight {
    background-color: #ff4e4e !important;
    border-radius: 2px !important;
    color: white !important;
  }

  .otherlevel-highlight {
    background-color: blue !important;
    border-radius: 2px !important;
    color: white !important;
  }
  .exemption-highlight {
    background-color: #67c23a !important;
    border-radius: 2px !important;
    color: white !important;
  }
}

/* 自定义Monaco编辑器悬浮框样式 */
:deep(.monaco-hover) {
  padding: 0px !important;
  max-width: 350px !important;
  max-height: 500px !important;
}

:deep(.monaco-hover-content) {
  transition: all 5.7s ease-in-out;
  padding: 0px !important;
}

:deep(.monaco-hover .hover-contents:not(.html-hover-contents)) {
  padding: 0 !important;
}

/* 使用通用后代选择器选中hover-row下所有层级的img标签 */
:deep(.hover-row) img {
  width: 100% !important;
  height: 100% !important;
  object-fit: contain !important;
}
</style>
