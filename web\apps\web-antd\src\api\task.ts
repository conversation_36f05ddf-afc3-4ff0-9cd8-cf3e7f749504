import { requestClient } from '#/api/request';
import type { ApiCommonResponse } from '#/types/api';
import type { HandoverServiceRequest, QueueCountDelayData } from '#/types/task';
import { setPayload } from '#/composables/common/use-front-payload';
import { $t } from '#/locales';
// 拉单
export function pullTasks(data?: any) {
  setPayload(data, 'customError', [
    {
      matchText: 'fail to get tasks',
      message: $t('api.task.pull.customError'),
    },
  ]);
  return requestClient.post('/manual/task/v2/pull', data);
}
// 首次请求角标和时效数据
export function getAllCount(
  data?: any,
): Promise<ApiCommonResponse<QueueCountDelayData>> {
  return requestClient.get('/manual/task/all_count', data);
}

// 打标
export function pushTask(data?: any) {
  return requestClient.post('/manual/task/v2/push', data);
}
// 家园获取实时单量
export function getHourEfficiency(data?: any) {
  return requestClient.post('/manual/task/v2/efficiency/hour', data);
}

// 队列中机审图片识别
export function getPreviewImageInfo(data?: any) {
  return requestClient.get('/manual/preview/imageinfo', { data });
}

// 获取用户标签
export function getUserTag(data?: any) {
  return requestClient.get('/manual/fuser/tags', { data });
}

// 全文评论批量提交
export function pushTextTaskBatch(data?: any) {
  return requestClient.post('/manual/task/v2/push_text/batch', data);
}

export function taskV2PushBatch(data?: any) {
  return requestClient.post('/manual/task/v2/push/batch', data);
}

export function getRecordsDetail(data?: any) {
  return requestClient.get('/manual/record/search', { data });
}
// 更新移交上级（移交上级）
export function handoverService(data?: HandoverServiceRequest) {
  return requestClient.post('/manual/record/handover_superior', data);
}
// 无法审核
export function previewIncapable(data?: any) {
  return requestClient.post('/manual/record/preview_incapable', data);
}
// 获取打标拒绝类型
export function getForbidType(data?: any) {
  return requestClient.get('/manual/record/forbid_types', { data });
}

// 获取原帖链接
export function getOriginArticleUrl(data?: any) {
  return requestClient.post('/manual/task/v2/preview/url', data);
}

export function releaseUserTaskService(data?: any) {
  return requestClient.post('/manual/task/user_release', data);
}
// 云主机埋点
export function cloudBury(data?: any) {
  return requestClient.post('/manual/common/data/bury', data);
}
export function updateComment(data?: any) {
  return requestClient.post('/manual/task/update_comment_directly', data);
  // 账号安全拉队列
}
export function getPullAccountList(data?: any) {
  return requestClient.get('/manual/task/pull_violation_user', { data });
}
// 账号安全更新操作
export function updateAccountService(data?: any) {
  return requestClient.post('/manual/task/update_violation_user', data);
}

// 获取紧集延迟和普通延迟
export function getDelayEfficiency(data?: any) {
  return requestClient.post('/manual/task/v2/oldest', data);
}
