import { requestClient } from '#/api/request';
import { ApiListResponse } from '#/types/api';
import type {
  AccountListApi,
  BatchLimitParams,
  DeleteGroupParams,
  opAccountParams,
  OperateAccountParams,
  Platform,
  QueryEntranceParams,
  SetGroupAccountParams,
} from '#/types/platform';

// 获取平台(业务)列表
export function getPlatformService(data: {
  enable: string;
}): Promise<ApiListResponse<Platform>> {
  return requestClient.get('/manual/control/get_platform', { data });
}

// 查询用户接口
export function getGroupAccount() {
  return requestClient.get('/manual/account/get_group_account');
}

// 获取供应商列表
export function supplierRegisterService(data?: any) {
  return requestClient.get('/manual/control/get_supplier_register', { data });
}

// 更新供应商列表
export function updateSupplierRegisterService(data: any) {
  return requestClient.post('/manual/control/set_supplier_register', data);
}

// 查询角色组列表
export function queryGroupList(data: any) {
  return requestClient.get('/manual/roles/query_group', { data });
}

// 删除角色组
export function deleteGroup(data: DeleteGroupParams) {
  return requestClient.post('/manual/roles/delete_group', data);
}

// 设置用户角色组
export function setGroupAccount(data: SetGroupAccountParams) {
  return requestClient.post('/manual/account/set_group_account', data);
}

// 查询权限入口

export function queryEntrance(data: QueryEntranceParams) {
  return requestClient.get('/manual/roles/query_entrance', { data });
}

// 新增/操作角色组
export interface OpGroupParams {
  // 根据实际请求参数补充接口字段，例如：
  // group_name: string;
  // permissions: number[];
  [key: string]: any;
}

export function opGroup(data: OpGroupParams) {
  return requestClient.post('/manual/roles/op_group', data);
}

// 新增编辑入口
export function opEntrance(data: any) {
  return requestClient.post('/manual/roles/op_entrance', data);
}
// 删除入口
export function deleteEntrance(data: any) {
  return requestClient.post('/manual/roles/delete_entrance', data);
}
export function getProductList(data: any) {
  return requestClient.get('/manual/control/product_list', { data });
}

// // 获取队列配置接口
export function getTaskConfig(data?: any) {
  return requestClient.get('/manual/control/get_task_simple_config', { data });
}

/**
 * 获取账号列表数据
 */
export function getAccountList(data: AccountListApi.PageFetchParams) {
  return requestClient.get('/manual/account/get_account_list', { data });
}

/**
 * 重置账号密码
 */

export function resetPasswdService(data: opAccountParams) {
  return requestClient.post('/manual/account/reset_account', data);
}

/**
 * 删除账号
 */
export function deleteAccount(data: opAccountParams) {
  return requestClient.post('/manual/account/delete_account', data);
}

/**
 * 临时登录
 */
interface TempLoginParams {
  uname: string;
  email: string;
}
export function emergencyVerify(data: TempLoginParams) {
  return requestClient.post('/manual/account/emergency_verify', data);
}

/**
 * 批量设置拉单限制
 */
export function setBatchTaskLimit(data: BatchLimitParams) {
  return requestClient.post('/manual/account/entry_limit', data);
}

/**
 * 新增/编辑账号
 */
export function operateAccount(data: OperateAccountParams) {
  return requestClient.post('/manual/account/operate_account', data);
}

// 预览优先级查询
export function queryPreviewPriority(data: any) {
  return requestClient.get('/manual/control/query_preview_priority', { data });
}
// 查询渠道列表
export function queryChannelService(data: any) {
  return requestClient.get('/manual/control/get_channel', { data });
}

// 查询故障隔离配置列表
export function queryIsolateService(data: any) {
  return requestClient.get('/manual/control/get_isolate', { data });
}

// 设置渠道列表
export function setIsolateService(data: any) {
  return requestClient.post('/manual/control/set_isolate', data);
}

// 获取登录日志
export function getLoginLogService(data: any) {
  return requestClient.get('/manual/record/get_login_log', { data });
}

// 获取审计日志
export function getAuditLogService(data: any) {
  return requestClient.get('/manual/control/get_account_log', { data });
}

// 代理白名单 增删改查
export function createWhiteList(data: any) {
  return requestClient.post('/manual/whitelist/create', data);
}

export async function getWhiteList() {
  return requestClient.get('/manual/whitelist/list');
}

export async function delWhiteList(data: any) {
  return requestClient.delete(`/manual/whitelist/delete?id=${data}`);
}

export async function updateWhiteList(data: any) {
  return requestClient.post('/manual/whitelist/update', data);
}

// 标签映射 查
export function querySubLabel(data: any) {
  return requestClient.get('/manual/control/query_sub_label', { data });
}
export async function addSubLabel(data: any) {
  return requestClient.post('/manual/control/add_sub_label', data);
}
export async function deleteSubLabel(data: any) {
  return requestClient.post('/manual/control/delete_sub_label', data);
}
export async function updateSubLabel(data: any) {
  return requestClient.post('/manual/control/update_sub_label', data);
}
export async function batchCreateSubLabel(data: any) {
  return requestClient.post('/manual/control/batch_create_sub_label', data);
}

// 获取防火墙申请列表
export function getApplyList(data?: any) {
  return requestClient.get('/manual/control/get_apply_list', { data });
}
