<script setup lang="ts">
import { ref } from "vue";
import type { VxeGridProps } from "#/adapter/vxe-table";

import TableTemplate from "#/components/table/index.vue";
import LexiconForm from "#/views/keywords/components/lexicon-form.vue";

import { getKeywordsWordBase, deleteKeywordsWordBase } from "#/api/keywords";
import { validateParams } from "#/utils/transform";
import { KEYWORD_TAG_MAP } from "#/constants/maps/keywords";
import { formatTimestampToDate } from "#/utils/time";

// 表格组件引用
const tableTemplateRef = ref<any>(null);
// 控制编辑模态框的显示
const isEditModalVisible = ref<boolean>(false);
// 当前编辑的行数据
const currentEditRow = ref<any>(null);
// 是否为编辑模式
const isEditMode = ref<boolean>(false);
// 模态框标题
const modalTitle = ref<string>("");

const searchOptions = {
  collapsed: false,
  schemas: {
    "1": [
      {
        component: "Input",
        fieldName: "id",
        label: "词库id",
        componentProps: {
          placeholder: "请输入",
        },
      },
      {
        component: "Input",
        fieldName: "name",
        label: "词库名",
      },
    ],
  },
  showCollapseButton: true,
  submitOnChange: false,
  wrapperClass: "grid-cols-5",
};

const columns: VxeGridProps["columns"] = [
  { type: "seq", width: 60, title: "序号" },
  { field: "id", title: "词库id" },
  { field: "name", title: "词库名" },
  { field: "explain", title: "词库说明" },
  {
    field: "tag",
    title: "词库类型",
    formatter: ({ cellValue }) => {
      if (!cellValue) return "-";
      // 根据tag值映射为不同的词库类型
      return KEYWORD_TAG_MAP[cellValue] || "通用";
    },
  },
  { field: "creator", title: "创建人" },
  {
    field: "use_type",
    title: "是否参与业务检查",
    formatter: ({ cellValue }) => (cellValue === "1" ? "是" : "否"),
  },
  {
    field: "modify_time",
    title: "最后更新时间",
    formatter: ({ cellValue }) => formatTimestampToDate(cellValue),
  },
  {
    field: "action",
    fixed: "right",
    title: "操作",
    width: 200,
    slots: { default: "action" },
  },
];

const getKeywordOpLogs = async ({
  page,
  pageSize,
  ...formValues
}: {
  page: number;
  pageSize: number;
  [key: string]: any;
}) => {
  try {
    const baseParams = {
      page: page - 1,
      limit: pageSize,
      ...formValues,
    };

    const queryString = validateParams(baseParams);
    const baseResponseData = await getKeywordsWordBase(queryString);

    return {
      items: baseResponseData.data.records,
      total: baseResponseData.data.count,
    };
  } catch (error) {
    return {
      items: [],
      total: 0,
    };
  }
};

const paginationOptions = {
  pageSize: 20,
  currentPage: 1,
};

// 处理编辑
const handleEdit = (row: any) => {
  isEditMode.value = !!row;
  // 设置模态框标题
  modalTitle.value = row ? "编辑词库" : "添加词库";
  // 设置当前编辑行数据
  currentEditRow.value = row ? { ...row } : null;
  // 显示编辑模态框
  isEditModalVisible.value = true;
};

// 模态框保存成功后刷新表格数据
const handleSaveSuccess = () => {
  isEditModalVisible.value = false;
  // 刷新表格数据
  tableTemplateRef.value?.refreshTable();
};

// 处理删除
const handleDelete = (row: any) => {
  deleteKeywordsWordBase(`id=${row.id}`).then(() => {
    tableTemplateRef.value?.refreshTable();
  });
};
</script>

<template>
  <div>
    <table-template
      ref="tableTemplateRef"
      :columns="columns"
      :pagination-options="paginationOptions"
      :query-method="getKeywordOpLogs"
      :search-options="searchOptions"
    >
      <template #action="{ row }">
        <a-button type="link" v-if="$isShowOpt('update')" @click="handleEdit(row)">编辑</a-button>
        <a-popconfirm
          title="确定要删除词库吗？"
          ok-text="确定"
          cancel-text="取消"
          @confirm="handleDelete(row)"
        >
          <a-button v-if="$isShowOpt('delete')" type="link" danger>删除</a-button>
        </a-popconfirm>
      </template>
      <template #toolbar-left>
        <a-button type="primary" v-if="$isShowOpt('add')" @click="handleEdit(null)">添加</a-button>
      </template>
    </table-template>
    <a-modal v-model:visible="isEditModalVisible" :footer="null" :title="modalTitle">
      <div v-if="isEditModalVisible">
        <lexicon-form
          ref="keywordFormRef"
          :formData="currentEditRow"
          :is-edit="isEditMode"
          @success="handleSaveSuccess"
        />
      </div>
    </a-modal>
  </div>
</template>
