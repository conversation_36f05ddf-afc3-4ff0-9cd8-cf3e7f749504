<script lang="ts" setup>
import { computed, ref, onMounted, provide, watch, shallowRef } from "vue";
import { getCurrentTimestamp, daysToSeconds, formatTimestampToDate } from "#/utils/time";
import useFetchBizOptions from "#/composables/task/use-fetch-biz-options";
import { Input, message } from "ant-design-vue";
import { useTaskStore } from "#/store";
import { IconifyIcon } from "@vben/icons";
import BizTabs from "#/components/task/BizTabs.vue";
import StandardForm from "#/components/form/index.vue";
import { getForbidInfo } from "#/api/forbidinfo";
import { useRoute } from "vue-router";
import { useUserStore } from "#/store";
import InfoCard from "./components/info-card.vue";
import FrequencyList from "./components/frequency-list.vue";
import SceneSelect from "#/components/select/scene.vue";
import { storeToRefs } from "pinia";
import { Page } from "@vben/common-ui";
import { LeftCircleOutlined, RightCircleOutlined } from "@ant-design/icons-vue";
import type { WordItem, ProcessedWords } from "./types";
import {
  useBasicInfo,
  useTextMachine,
  useNlp,
  useCntv,
  useImageMachine,
  useTranslate,
  useGov,
  useShumei,
  useMedia,
  useBlackImage,
} from "./composables";
const route = useRoute();
const formRef = ref<any>(null);
const { BizOptions, FetchBizOptions } = useFetchBizOptions();
onMounted(async () => {
  await FetchBizOptions();
  loading.value = false;

  if (route.query && Object.keys(route.query).length > 0) {
    const values = {
      fileinx: route.query.fileinx || "",
      extra_id: route.query.extra_id || "",
      scene: route.query.scene || "",
    };
    await getData(values);
  }
});

provide("BizOptions", BizOptions);
const loading = ref(true);
const userStore = useUserStore();
const taskStore = useTaskStore();
const { biz } = storeToRefs(taskStore);
const layout = "horizontal";
const formContainerClass = "flex flex-wrap";
const wrapperClass = computed(() => {
  return biz.value.startsWith("docer") ? `grid-cols-5 gap-1` : `grid-cols-4 gap-1`;
});

const commonConfig = {
  colon: true,
  componentProps: {
    class: "w-full",
  },
  labelWidth: 60,
};

const actionBtnClass = "ml-[10px]";
const dataList = ref<Record<string, any>>({});
const qrInfo = ref<string[]>([]);
const qrCodeVisible = ref(false);
const originalDetailLength = ref(0);
const baseSchema = [
  {
    component: shallowRef(Input),
    componentProps: {
      placeholder: "fileID",
      defaultValue: route.query.fileinx || "",
    },
    fieldName: "fileinx",

    label: biz.value === "home_comment" ? "评论id" : "fileID",
    colon: true,
  },
  {
    component: shallowRef(SceneSelect),
    componentProps: {
      biz: biz,
    },
    defaultValue: route.query.scene || "",
    fieldName: "scene",
    label: "场景",
  },
  {
    component: shallowRef(Input),
    componentProps: {
      placeholder: "附件id",
      defaultValue: route.query.extra_id || "",
    },
    fieldName: "extra_id",

    label: "附件id",
    colon: true,
  },
  {
    component: shallowRef(Input),
    dependencies: {
      show() {
        return biz.value.startsWith("docer");
      },
      triggerFields: [""],
    },
    componentProps: {
      placeholder: "资源id",
    },
    fieldName: "resource_id",
    label: "资源id",
    colon: true,
  },
];
const createTime = computed(() => dataList.value.record?.create_time);
const { basicInfoOptions } = useBasicInfo(createTime);
// 文本机审
const { textMachineOptions } = useTextMachine();
// nlp
const { nlpOptions } = useNlp();
// 央视机审结果
const { cntvOptions } = useCntv();
const { imageMachineOptions } = useImageMachine();
// 翻译文本机审
const { translateOptions } = useTranslate();
// 红头文件
const { govOptions } = useGov();
// 数美机审
const { shumeiOptions } = useShumei();
// 音视频
const { mediaOptions } = useMedia();
// 图片黑库
const { blackImageOptions } = useBlackImage();
watch(
  () => biz.value,
  () => {
    // 当 biz 变化时，清空 dataList
    dataList.value = {};
    qrInfo.value = [];
    qrCodeVisible.value = false;
    originalDetailLength.value = 0;
  }
);

const getData = async (values: any) => {
  try {
    let { fileinx, scene, extra_id, resource_id } = values;
    const params: Record<string, any> = {
      user_id: userStore.userid,
      from: taskStore.biz,
      limit: 5,
      fileinx,
      scene,
      extra_id,
      resource_id,
    };
    // 请求数据
    const response = await getForbidInfo(params);
    if (response?.data) {
      // 先把原始 data 给到 reactive
      dataList.value = response.data;

      // —— 如果 detail 是空数组，则 push 一个“占位”对象 —— 
      const d = dataList.value.detail;
      if (Array.isArray(d) && d.length === 0) {
        dataList.value.detail = [
          {
            // 这里不需要完整结构，只要保证模板层的 v-if 跳过机审阶段
            worker: null,
            reco: { reco_empty: true },
            // 如果后面有 word_freq 等也可以补空数组
            word_freq: [],
          },
        ];
      }

      // 保存接口返回时真正的 detail 长度，用于 overTimeTip 之类的判断
      originalDetailLength.value = response.data.detail?.length || 0;

      // 再去处理关键词、格式化等
      processData();
    } else {
      message.warning(response.message);
    }
  } catch (error) {
    message.error("查询失败，请稍后重试");
  }
};


// 处理单个关键词文本
const formatWordText = (w: WordItem): string => {
  const { word, orig_word, decorate = [], orig_decorate = [] } = w;
  // 处理主文本
  let mainText = word;
  if (decorate.length > 0) {
    mainText += `+${decorate.join(",")}`;
  }
  // 如果没有原始文本或原始文本与主文本相同，直接返回主文本
  if (!orig_word || orig_word === word) {
    return mainText;
  }
  // 处理原始文本
  let originalText = orig_word;
  if (orig_decorate.length > 0) {
    originalText += `+${orig_decorate.join(",")}`;
  }

  // 如果原始文本处理后与主文本相同，也直接返回主文本
  if (originalText === mainText) {
    return mainText;
  }

  // 返回带转换标记的文本
  return `${originalText}->${mainText}`;
};

// 处理关键词分类
const processKeywords = (wordList: WordItem[] = []): ProcessedWords => {
  const words = {
    w1_10: [] as string[],
    w1_00: [] as string[],
    w2_00: [] as string[],
    w200: [] as string[],
  };

  wordList.forEach((w) => {
    const formattedWord = formatWordText(w);
    switch (true) {
      case w.kind === 1 && w.level === 10:
        words.w1_10.push(formattedWord);
        break;
      case w.kind === 1:
        words.w1_00.push(formattedWord);
        break;
      case w.kind === 2:
        words.w2_00.push(formattedWord);
        break;
      case w.kind === 200:
        words.w200.push(formattedWord);
        break;
    }
  });

  return {
    w1_10: words.w1_10.join("、") || "无",
    w1_00: words.w1_00.join("、") || "无",
    w2_00: words.w2_00.join("、") || "无",
    w200: words.w200.join("、") || "无",
  };
};
// 主要用于处理关键词分类
const processData = () => {
  if (!dataList.value.detail) return;

  dataList.value.detail.forEach((item: any) => {
    // 对workder处理
    if (item.worker?.word_list) {
      item.worker.words = processKeywords(item.worker.word_list);
    }
    // 处理qrcode_detail的words字段
    if (item.reco?.qrcode_detail?.word_list) {
      const qrWords = processKeywords(item.reco.qrcode_detail.word_list);
      item.reco.qrcode_detail.words = qrWords;
    }
    // 红头文件
    if (item.gov?.word_list) {
      const qrWords = processKeywords(item.gov.word_list);
      item.gov.words = qrWords;
    }
    // 翻译文本机审
    if (item.translate?.word_list) {
      const qrWords = processKeywords(item.translate.word_list);
      item.translate.words = qrWords;
    }
  });
};

const overTimeTip = computed(() => {
  const modifyTime = dataList.value.record?.modify_time || 0;
  const timeDiff = getCurrentTimestamp() - modifyTime;
  return timeDiff > daysToSeconds(90) && originalDetailLength.value === 0
    ? "(日志可能被清理)"
    : "";
});

// 缺失的判断方法
const isLink = (str: string): boolean => {
  return !!/^(http:|https:)/gi.test(str);
};

// 二维码链接跳转方法
const qrLink = (item: string): void => {
  if (isLink(item)) {
    window.open(item, "_blank");
  }
};
</script>

<template>
  <Page>
    <a-skeleton
      v-if="loading"
      active
      :paragraph="false"
      class="m-3 translate-y-1 sm:w-[300px] md:w-[600px] lg:w-[1200px]"
    />
    <biz-tabs v-else class="max-w-[calc(100vw-600px)]" />
    <standard-form
      ref="formRef"
      :key="biz"
      :schema="baseSchema"
      :layout="layout"
      :wrapper-class="wrapperClass"
      :common-config="commonConfig"
      :showDefaultActions="true"
      :action-btn-class="actionBtnClass"
      @submit="getData"
      class="my-4"
      action-wrapper-class="text-left col-auto"
      :form-container-class="formContainerClass"
    />

    <div
      v-if="dataList?.record && dataList.detail"
      class="mt-5 flex w-full flex-row overflow-hidden"
    >
      <a-carousel
        :autoplay="false"
        indicator-position="outside"
        class="w-[95%]"
        :dots="true"
        arrows
      >
        <template #prevArrow>
          <div class="custom-slick-arrow" style="left: 10px; z-index: 1">
            <left-circle-outlined />
          </div>
        </template>
        <template #nextArrow>
          <div class="custom-slick-arrow" style="right: 10px">
            <right-circle-outlined />
          </div>
        </template>
        <div v-for="(item, index) in dataList.detail" :key="index">
          <div
            class="flex h-full max-h-[calc(100vh-250px)] w-full flex-row flex-nowrap overflow-auto"
          >
            <div class="ml-[50px] w-[78%] min-w-[780px] overflow-auto p-5">
              <a-timeline>
                <!-- 这一行的样式是为了消除Icon的默认边框 -->
                <a-timeline-item
                  class="[&_.ant-timeline-item-head]:border-none [&_.ant-timeline-item-head]:bg-transparent"
                >
                  <template #dot>
                    <IconifyIcon icon="ant-design:bell-outlined" height="24" width="24" />
                  </template>

                  <info-card
                    :data="dataList.record"
                    :options="basicInfoOptions"
                    :card-title="`${formatTimestampToDate(
                      dataList.record?.modify_time
                    )}文档被【${dataList.record?.reviewer_name || '系统'}】操作`"
                  />
                  <div
                    v-if="index == 0"
                    class="new-flag absolute left-[-20px] top-[29px] h-0 w-[80px] rotate-[-45deg] border-b-[26px] border-l-[26px] border-r-[26px] border-b-[#ff0000b3] border-l-transparent border-r-transparent"
                  >
                    <span
                      class="relative left-0 top-[5px] block w-[30px] font-bold text-white"
                      >最新</span
                    >
                  </div>
                </a-timeline-item>

                <a-timeline-item v-if="item.worker">
                  <info-card
                    :data="item.worker"
                    :options="textMachineOptions"
                    :card-title="`${formatTimestampToDate(item.worker.nowtime)} 文本机审`"
                  />
                  <info-card
                    :data="item.nlp"
                    :options="nlpOptions(item.nlp)"
                    class="mt-4"
                  />
                </a-timeline-item>

                <!-- 央视机审结果待检查 -->
                <a-timeline-item v-if="item.worker?.cntv_ai_res">
                  <info-card
                    :data="item.worker"
                    :options="cntvOptions"
                    :card-title="`${formatTimestampToDate(
                      item.worker.nowtime
                    )} 央视机审结果`"
                  />
                </a-timeline-item>

                <a-timeline-item v-if="item.reco && !item.reco.reco_empty">
                  <info-card
                    :data="item.reco"
                    :options="imageMachineOptions"
                    :card-title="`${formatTimestampToDate(item.reco.nowtime)} 图片机审`"
                  />
                </a-timeline-item>

                <!-- 翻译文本机审 -->
                <a-timeline-item v-if="item.translate?.nowtime">
                  <info-card
                    :data="item.translate"
                    :options="translateOptions"
                    :card-title="`${formatTimestampToDate(
                      item.translate.nowtime
                    )} 翻译文本机审`"
                  />
                </a-timeline-item>

                <!-- 红头文件检测 -->
                <a-timeline-item v-if="item.gov">
                  <info-card
                    :data="item.gov"
                    :options="govOptions"
                    :card-title="`${formatTimestampToDate(
                      item.gov.nowtime
                    )} 红头文件检测`"
                  />
                </a-timeline-item>

                <!-- 数美机审待检查 -->
                <a-timeline-item v-if="item.worker?.sm_res_ai">
                  <info-card
                    :data="item.worker"
                    :options="shumeiOptions"
                    :card-title="`${formatTimestampToDate(
                      item.worker.sm_callback_time
                    )} 数美机审`"
                  />
                </a-timeline-item>

                <!-- 音视频机审模块 -->
                <a-timeline-item v-if="item.media">
                  <info-card
                    :data="item.media"
                    :options="mediaOptions"
                    :card-title="`${formatTimestampToDate(
                      item.media.nowtime
                    )} 音视频机审`"
                  />
                </a-timeline-item>

                <!-- 图片黑库检测模块 -->
                <a-timeline-item v-if="item.rear_image_check_info">
                  <info-card
                    :data="item.rear_image_check_info"
                    :options="blackImageOptions"
                    :card-title="`${formatTimestampToDate(
                      item.rear_image_check_info.now_time
                    )} 图片黑库检测`"
                  />
                </a-timeline-item>

                <!--结果 -->
                <a-timeline-item>
                  <div class="mb-2.5 text-sm text-gray-600">操作完成</div>
                  <a-card>
                    <p class="title text-lg">
                      结束<span class="text-sm text-[#f56c6c]">{{ overTimeTip }}</span>
                    </p>
                  </a-card>
                </a-timeline-item>
              </a-timeline>
            </div>

            <!-- 高频词展示 -->
            <div
              class="ml-5 mt-5 max-h-[calc(100vh-150px)] w-1/5 min-w-[270px] overflow-auto"
            >
              <frequency-list
                :dataList="item.word_freq || []"
                headerLabel="高频词"
                valueLabel="出现次数"
                headerBgColor="#e7e9eb"
                :itemHeight="40"
              />
            </div>
          </div>
        </div>
      </a-carousel>
      <a-modal title="二维码信息" v-model:open="qrCodeVisible" width="30%">
        <div v-for="(item, index) in qrInfo" :key="index" class="qrcode-item">
          <span :class="{ isLink: isLink(item) }" @click="qrLink(item)">
            {{ item }}
          </span>
        </div>
      </a-modal>
    </div>
  </Page>
</template>

<style scoped>
:deep(.custom-slick-arrow) {
  width: 25px;
  height: 25px;
  font-size: 25px;
  color: #fff;
  background-color: rgba(31, 45, 61, 0.11);
  transition: ease all 0.3s;
  opacity: 0.3;
  z-index: 1;
}
:deep(.custom-slick-arrow:before) {
  display: none;
}
:deep(.custom-slick-arrow:hover) {
  color: #fff;
  opacity: 0.5;
}
</style>
