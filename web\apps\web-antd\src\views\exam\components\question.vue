<script setup>
import { onMounted, ref, reactive, h, watch } from 'vue';
import {
  PlusOutlined,
  MinusOutlined,
  DeleteOutlined,
  CloseOutlined,
  CloseCircleOutlined,
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import { generatePresignedUrl } from '#/api/tool';
import {
  createQuestion,
  updateCart,
  queryCart,
  updateQuestion,
} from '#/api/exam';
import ImgView from '#/components/image/index.vue';
import AutoWidthInput from './AutoWidthInput.vue';
import { TYPES, DIFFICULTIES, TAGS } from '#/constants/maps/exam';
import { mapToOptions } from '#/utils/transform';
import StandardForm from '#/components/form/index.vue';
import { usePresignedUpload } from '#/composables/common/use-presigned-upload';
import { success } from '#/utils/toast';

const { handleUpload } = usePresignedUpload();
const emits = defineEmits([
  'copy-question',
  'delete-question',
  // 'update:question',
  'update:questionCopy',
  'update:isCreated',
  'update:questionToCartNum',
  'update:readOnly',
  'update:isAddAll', //一键添加
  'update:isRemoveAll', //一键移除
  'update:isTestManage',
  'update:isEdit',
]);
const props = defineProps({
  questionCard: {
    type: Object,
    default: () => ({}),
  },
  order: {
    type: Number,
    default: 0,
  },
  readOnly: {
    type: Boolean,
    default: false,
  },
  questionToCartNum: {
    type: Number,
  },
  isAddAll: {
    type: Boolean,
  },
  isRemoveAll: {
    type: Boolean,
  },
  isTestManage: {
    type: Boolean,
    default: false,
  },

  isEdit: {
    type: Boolean,
    default: false,
  },
  isShowInfo: {
    type: Boolean,
    default: false,
  },
  isCreate: {
    // 试题录入
    type: Boolean,
    default: false,
  },
  isCreated: {
    //试题是否加入题库
    type: Boolean,
    default: false,
  },
  questionCopy: {
    type: Object,
    default: () => ({}),
  },
});

const judgeChoice = reactive({
  true: '正确',
  false: '错误',
});
const question = ref(props.questionCard);
const questionsInCart = ref([]);
const optionKey = ref('');
const addLoading = ref(false);
const createLoading = ref(false);
const answerAnalysisArea = ref('');
const analysisOpen = ref(false);
const charCode = ref('b'.charCodeAt(0));
// const num = ref(props.questionToCartNum);
const autoSize = { minRows: 1, maxRows: 4 };
const answerOpen = ref(false);
const titleImgInput = ref(null);
const fileInput = ref(null);

watch(
  () => props.questionToCartNum,
  (newVal) => {
    console.log('单个试题，购物车中数量变化', newVal);
    // num.value = newVal;
  },
);
const selectForm = ref(null);
const rules = ref('');
const schema = [
  {
    component: 'Select',
    defaultValue: question.value.question_type,
    componentProps: {
      options: mapToOptions(TYPES),
      disabled: true,
      style: 'width:100%',
    },

    fieldName: 'question_type',
    label: '题型：',
  },
  {
    component: 'Select',
    defaultValue: question.value.difficulty,
    componentProps: {
      options: mapToOptions(DIFFICULTIES).filter((item) => item.value !== 0),
      disabled: props.readOnly,
      placeholder: '请选择',
      style: 'width:100%',
    },
    fieldName: 'difficulty',
    label: '难度：',
    rules: rules,
  },
  {
    component: 'Select',
    defaultValue: question.value.tag,
    componentProps: {
      options: mapToOptions(TAGS),
      disabled: props.readOnly,
      placeholder: '请选择',
      style: 'width:100%',
    },
    fieldName: 'tag',
    label: '标签：',
    rules: rules,
  },
];
const wrapperClass = 'grid grid-cols-3 gap-4';
watch(
  () => props.questionCard,
  (newVal) => {
    question.value = newVal;

    initQuestion();
  },
  {
    deep: true,
  },
);

initQuestion();

function initQuestion() {
  if (!props.readOnly) rules.value = 'required';
  if (question.value.id) {
    // 展示题库

    // 处理多选题答案
    if (question.value.question_type === 2) {
      if (typeof question.value.answer === 'string') {
        question.value.answer = question.value.answer.split('');
      } // 针对多选的答案""abc"
    }

    // 处理填空题答案
    if (question.value.question_type === 4) {
      if (typeof question.value.answer === 'string') {
        question.value.answer = question.value.answer.split(/，|,/);
      } // 针对填空的答案"1,2,3"
    }

    // 确保选项中的 analyse 字段存在
    if ([1, 2].includes(question.value.question_type) && question.value.content && question.value.content.choice) {
      for (const key in question.value.content.choice) {
        if (!question.value.content.choice[key].hasOwnProperty('analyse')) {
          question.value.content.choice[key].analyse = '';
        }
      }
    }
  }
}
async function addUpdateBefore() {
  try {
    // 表单验证
    const res = await selectForm.value.formApi.validate();
    if (!res.valid) {
      console.log("表单验证失败");
      return true;
    }

    // 验证题目标题
    if (!question.value.title.msg) {
      message.error('请填写题目标题');
      return true;
    }

    // 验证答案
    if (!question.value.answer) {
      message.error('请设置题目答案');
      return true;
    } else if (
      question.value.question_type === 2 &&
      question.value.answer.length < 2
    ) {
      message.error('多选题的答案不能少于2个');
      return true;
    } else if (question.value.question_type === 4) {
      const firstNullBlank = question.value.answer.find(
        (element) => element === '',
      );
      if (firstNullBlank === '') {
        message.error('请设置题目答案');
        return true;
      }
    }

    // 所有验证通过
    return false;
  } catch (error) {
    console.error("验证表单时发生错误:", error);
    message.error('验证表单失败，请重试');
    return true;
  }
}

function setaddUpdateBeforeAnswer() {
  if (question.value.question_type === 2) {
    //多选

    question.value.answer.sort();
    return question.value.answer.join('');
  } else if (question.value.question_type === 4) {
    //填空
    return question.value.answer.join(',');
  } else return question.value.answer;
}

async function updateChoiceImgs() {
  const content = {
    analyse: question.value.content.analyse || '',
    choice: {},
  };
  for (const key in question.value.content.choice) {
    content.choice[key] = {};
    content.choice[key].msg = question.value.content.choice[key].msg;
    // 保存每个选项的解析
    content.choice[key].analyse = question.value.content.choice[key].analyse || '';
    content.choice[key].images = question.value.content.choice[key].presignKeys || [];
  }

  return content;
}
async function updateTitleImgsKeys() {
  const title = {
    msg: '',
    images: [],
  };
  title.msg = question.value.title.msg;
  title.images = question.value.title.presignKeys;

  return title;
}
async function addToBank() {
  console.log('试题录入', props.isCreate);
  if (props.isCreate) {
    //试题录入
    const isNull = await addUpdateBefore();
    console.log('isNUll', isNull);
    if (isNull) return;
    createLoading.value = true;
  }

  const title = await updateTitleImgsKeys();
  const answer = setaddUpdateBeforeAnswer();

  let content = null;

  if ([3, 4, 5].includes(question.value.question_type)) {
    content = question.value.content;
  } else {
    content = await updateChoiceImgs();
  }
  const res = await createQuestion({
    question_type: question.value.question_type,
    difficulty: question.value.difficulty,
    tag: question.value.tag,
    title: title,
    content: content,
    answer: answer,
  });

  if (res && res.result) {
    question.value.id = res.data;

    if (props.isCreate) {
      createLoading.value = false;
      emits('update:isCreated', true);
      message.success('已成功加入题库');
      deleteQuestion();
    }
    return true;
  } else {
    message.error(res.message);
    return false;
  }
}
// watch(num, (newVal) => {
//   console.log('num变化', newVal);
//   emits('update:questionToCartNum', newVal);
//   if (question.value.isInCart) emits('update:readOnly', true);
// });
async function removeFromCart() {
  question.value.isInCart = false;
  updateAddRemoveAllBtn();

  const num = props.questionToCartNum - 1;
  emits('update:questionToCartNum', num);
  await queryCartData();
  const index = questionsInCart.value.findIndex(
    (item) => item.question_id === question.value.id,
  );
  questionsInCart.value.splice(index, 1);

  updateCartData();
}
function updateAddRemoveAllBtn() {
  emits('update:isAddAll', false);
  emits('update:isRemoveAll', false);
}
async function addToCart() {
  if (!question.value.id) {
    const isNull = await addUpdateBefore();
    if (isNull) return;

    addLoading.value = true;
    const isAddToBank = await addToBank();
    if (!isAddToBank) {
      addLoading.value = false;
      return;
    }
  }

  question.value.isInCart = true;

  const num = props.questionToCartNum + 1;
  console.log('将题目添加到购物车中', num);
  emits('update:questionToCartNum', num);
  emits('update:readOnly', true);
  addLoading.value = false;
  const title = await updateTitleImgsKeys();
  await queryCartData();

  questionsInCart.value.push({
    question_id: question.value.id,
    score: 0,
    question_type: question.value.question_type,
    title: title,
  });

  updateCartData();
}
async function updateCartData() {
  const res = await updateCart({
    content: questionsInCart.value,
  });
}
async function queryCartData() {
  const res = await queryCart();

  if (res && res.result) {
    if (res.data.content) {
      questionsInCart.value = res.data.content;
    }
  }
}
function updateBlanks() {
  const newBlanks = question.value.title.msg.split('_').length - 1;
  if (newBlanks < question.value.answer.length) {
    question.value.answer.pop();
  } else if (newBlanks > question.value.answer.length) {
    question.value.answer.push('');
  }
}
function addImgIntitle() {
  titleImgInput.value.click();
}
async function handleTitleImgs(event) {
  const file = event.target.files[0];
  if (file) {
    const img = await readFileAsDataURL(file);
    if (!question.value.title.images) {
      question.value.title.images = [];
      question.value.title.presignKeys = [];
    }
    // updateTitleImgs(img);
    question.value.title.images.push(img);

    // 获取url和key值
    // const res = await generatePresignedUrl({
    //   file_name: file.name,
    //   expire: false,
    // });

    // if (res && res.result) {
    //   const presignUrl = res.data.url;
    //   const presignKey = res.data.key;
    //   question.value.title.presignKeys.push(presignKey);

    //   // 将图片文件上传到云存储
    //   const uploadResponse = await fetch(presignUrl, {
    //     method: 'PUT',
    //     headers: {
    //       'x-amz-acl': 'private',
    //       'Content-Type': 'application/octet-stream',
    //     },
    //     body: file, // 这里传入的是图片文件
    //   });

    //   if (!uploadResponse.ok) {
    //     console.log('Failed to upload image');
    //   }
    // }
    await handleUpload(generatePresignedUrl, file, {
      success: (key) => {
        question.value.title.presignKeys.push(presignKey);
      },
    });
    event.target.value = '';
  }
}
function readFileAsDataURL(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
}
function addImgInOption(key, index) {
  optionKey.value = key;

  fileInput.value.click();
}
async function handleFiles(event) {
  const file = event.target.files[0];
  if (file) {
    const img = await readFileAsDataURL(file);

    if (!question.value.content.choice[optionKey.value].images) {
      question.value.content.choice[optionKey.value].images = [];
      question.value.content.choice[optionKey.value].presignKeys = [];
    }

    question.value.content.choice[optionKey.value].images.push(img);
    // 获取url和key
    // const res = await generatePresignedUrl({
    //   file_name: file.name,
    //   expire: false,
    // });
    // if (res && res.result) {
    //   const presignUrl = res.data.url;
    //   const presignKey = res.data.key;

    //   question.value.content.choice[optionKey.value].presignKeys.push(
    //     presignKey,
    //   );
    //   // 将图片文件上传到云存储
    //   const uploadResponse = await fetch(presignUrl, {
    //     method: 'PUT',
    //     headers: {
    //       'x-amz-acl': 'private',
    //       'Content-Type': 'application/octet-stream',
    //     },
    //     body: file, // 这里传入的是图片文件
    //   });

    //   if (!uploadResponse.ok) {
    //     console.log('Failed to upload image');
    //   }
    // }
    await handleUpload(generatePresignedUrl, file, {
      success: (key) => {
        question.content.choice[optionKey.value].presignKeys.push(key);
      },
    });
    event.target.value = '';
  }
}
function addOption() {
  if (props.isTestManage) {
    const keys = Object.keys(question.value.content.choice);
    charCode.value = keys[keys.length - 1].charCodeAt(0);
  }

  charCode.value += 1;
  const char = String.fromCharCode(charCode.value);
  question.value.content.choice[char] = {
    msg: '',
    images: [],
    presignKeys: [],
    analyse: '', // 添加解析字段
  };
}
function deleCurOption(key) {
  const count = Object.keys(question.value.content.choice).length;
  if (count > 2) {
    delete question.value.content.choice[key];
  } else {
    message.error('至少设置两个选项');
  }
  reassignContentKeys();
}
function reassignContentKeys() {
  // 重新分配键值
  const keys = Object.keys(question.value.content.choice);
  const newChoice = {};
  for (let i = 0; i < keys.length; i++) {
    const newKey = String.fromCharCode(97 + i); // 'a' 的 ASCII 码是 97
    newChoice[newKey] = question.value.content.choice[keys[i]]; // 重新分配键值
  }
  question.value.content.choice = newChoice;
}
function addBlank() {
  question.value.title.msg += '_ ';
  question.value.answer.push('');
}
function deleteTitleImg(index) {
  question.value.title.images.splice(index, 1);
  question.value.title.presignKeys.splice(index, 1);
}

function deleteImg(key, index) {
  question.value.content.choice[key].images.splice(index, 1);
  question.value.content.choice[key].presignKeys.splice(index, 1);
}
function copyQuestion() {
  emits('update:questionCopy', question.value);

  emits('copy-question');
}
function deleteQuestion() {
  emits('delete-question');
}
function setAnswerAnalysis() {
  if (question.value.question_type === 5) {
    question.value.answer = answerAnalysisArea.value;
  } else {
    question.value.content.analyse = answerAnalysisArea.value;
  }

  answerAnalysisArea.value = question.value.content.analyse;
  analysisOpen.value = false;
}
function clickCancelBtn() {
  emits('update:isTestManage', false);
  emits('update:isEdit', false);
}
async function clickOkBtn() {
  console.log("确认事件触发");

  if (!props.readOnly) {
    try {
      // 使用 await 等待 addUpdateBefore 函数的结果
      const isNull = await addUpdateBefore();
      if (isNull) return;

      // 等待更新试题数据
      const updateSuccess = await updateQuestionData();

      if (updateSuccess) {
        console.log("更新成功");
        emits('update:isEdit', true);
        emits('update:isTestManage', false);
      } else {
        console.log("更新失败");
        // 更新失败时不关闭模态框
        return;
      }
    } catch (error) {
      console.error("更新试题时发生错误:", error);
      message.error("更新试题失败，请重试");
      return;
    }
  } else {
    console.log("只读模式，不更新数据");
    emits('update:isEdit', false);
    emits('update:isTestManage', false);
  }
}
async function updateQuestionData() {
  try {
    const title = await updateTitleImgsKeys();
    const content = await updateChoiceImgs();
    const answer = setaddUpdateBeforeAnswer();

    const questionData = {
      id: question.value.id,
      difficulty: question.value.difficulty,
      tag: question.value.tag,
      title: title,
      answer: answer,
      remarks:question.value.remarks
    };
    if ([3, 4, 5].includes(question.value.question_type)) {
      questionData.content = question.value.content;
    } else {
      questionData.content = content;
    }

    console.log("提交更新试题数据:", questionData);
    const res = await updateQuestion(questionData);

    if (res && res.result) {
      if (res.message === 'success') {
        message.success('更改成功');
        return true;
      }
    }

    // 如果没有成功响应，显示错误信息
    if (res && res.message) {
      message.error(res.message || '更新失败');
    } else {
      message.error('更新失败，请重试');
    }
    return false;
  } catch (error) {
    console.error("更新试题数据时发生错误:", error);
    message.error('更新失败，请重试');
    return false;
  }
}
const getPopupContainer = (trigger) => {
  return trigger.parentElement;
};
function handleValuesChange(values) {
  Object.assign(question.value, values);
}
</script>
<template>
  <a-card :bordered="false" class="mb-5 mt-5 w-full min-w-[800px]">
    <div class="w-full overflow-x-hidden">
      <div class="flex h-[36px] w-full items-center justify-between">
        <standard-form
          ref="selectForm"
          :schema="schema"
          :showDefaultActions="false"
          :wrapper-class="wrapperClass"
          :handle-values-change="handleValuesChange"
          class="w-4/5"
        ></standard-form>
        <a-button
          v-if="isCreate"
          type="primary"
          size="small"
          @click="addToBank"
          :icon="h(PlusOutlined)"
          :loading="createLoading"
        >
          加入
        </a-button>
        <a-button
          v-else-if="question.isInCart === true"
          size="small"
          @click="removeFromCart"
          type="default"
          :icon="h(MinusOutlined)"
        >
          移除
        </a-button>

        <a-button
          v-else-if="question.isInCart === false"
          size="small"
          @click="addToCart"
          type="primary"
          :icon="h(PlusOutlined)"
          :loading="addLoading"
          >添加</a-button
        >
      </div>

      <div class="mb-2.5 mt-4 w-full text-[18px]">
        <span v-if="order !== 0">{{ order + '.' }}</span>
        <a-textarea
          v-if="question.question_type === 4"
          class="ml-1 w-[90%] rounded-none border-x-0 border-b border-t-0 pl-px text-[16px] shadow-none outline-none hover:border-x-0 hover:border-t-0 focus:border-x-0 focus:border-t-0 focus:shadow-none focus:outline-none"
          placeholder="请输入问题"
          v-model:value="question.title.msg"
          @change="updateBlanks"
          :readOnly="props.readOnly"
          :autoSize="autoSize"
        />
        <a-textarea
          v-else
          class="ml-1 w-[90%] rounded-none border-x-0 border-b border-t-0 pl-px text-[16px] shadow-none outline-none hover:border-x-0 hover:border-t-0 focus:border-x-0 focus:border-t-0 focus:shadow-none focus:outline-none"
          placeholder="请输入问题"
          v-model:value="question.title.msg"
          :readOnly="props.readOnly"
          :autoSize="autoSize"
        />
        <a-button
          v-if="!props.readOnly"
          class="w-[5%] pl-2.5"
          type="link"
          @click="addImgIntitle"
          >添加图片</a-button
        >

        <input
          type="file"
          ref="titleImgInput"
          class="hidden"
          accept=".jpg,.jpeg,.png,.bmp"
          @change="handleTitleImgs"
        />
        <input
          type="file"
          ref="fileInput"
          class="hidden"
          accept=".jpg,.jpeg,.png,.bmp"
          @change="handleFiles"
        />
        <div
          v-if="question.title.images && question.title.images.length"
          class="mt-2 flex"
        >
          <div
            v-for="(item, index) in question.title.images"
            class="relative mr-2.5 h-[150px] w-[150px]"
          >
            <img-view class="h-[150px] w-[150px]" :src="item"></img-view>
            <CloseCircleOutlined
              v-if="!props.readOnly"
              class="z-100 absolute right-[-5px] top-[-2px] h-2.5 w-2.5 cursor-pointer"
              @click="deleteTitleImg(index)"
            />
          </div>
        </div>
      </div>
      <!-- 简答 -->
      <a-textarea
        v-if="question.question_type === 5"
        :autoSize="{ minRows: 2, maxRows: 6 }"
        placeholder="填写者回答区"
        readOnly
      >
      </a-textarea>
      <!-- 判断 -->
      <div
        v-else-if="question.question_type === 3"
        class="mb-2.5 w-full"
        v-for="(value, key, index) in judgeChoice"
      >
        <div class="mb-1.25 flex w-full items-center">
          <span>{{ String.fromCharCode(64 + index + 1) + '.' }}</span>
          <a-input
            v-if="question.question_type === 3"
            class="ml-1 w-[90%] rounded-none border-x-0 border-b border-t-0 pl-px shadow-none outline-none hover:border-x-0 hover:border-t-0 focus:border-x-0 focus:border-t-0 focus:shadow-none focus:outline-none"
            :value="value"
            readOnly="true"
          />
        </div>
      </div>
      <!-- 单选，多选 -->
      <div
        v-else-if="question.question_type === 1 || question.question_type === 2"
        class="mb-2.5 w-full"
        v-for="(value, key, index) in question.content.choice"
        :key="key"
      >
        <div class="mb-1.25 flex w-full items-center justify-between">
          <div class="flex w-[98%] items-center">
            <span>{{ String.fromCharCode(64 + index + 1) + '.' }}</span>

            <div class="ml-1.25 w-[98%]">
              <a-textarea
                class="ml-1 w-[90%] rounded-none border-x-0 border-b border-t-0 pl-px shadow-none outline-none hover:border-x-0 hover:border-t-0 focus:border-x-0 focus:border-t-0 focus:shadow-none focus:outline-none"
                :placeholder="`选项${index + 1}`"
                v-model:value="value.msg"
                :readonly="readOnly"
                :autoSize="autoSize"
              />

              <a-button
                v-if="!props.readOnly"
                class="w-[5%] pl-2.5"
                type="link"
                @click="addImgInOption(key, index)"
                >添加图片</a-button
              >
            </div>
          </div>

          <div
            v-if="!props.readOnly"
            class="w-[2%]"
            @click="deleCurOption(key)"
          >
            <CloseOutlined class="cursor-pointer" />
          </div>
        </div>
        <div v-if="value.images && value.images.length" class="mt-2 flex">
          <div
            v-for="(item, imgIndex) in value.images"
            class="relative mr-2.5 h-[120px] w-[120px]"
          >
            <img-view class="h-[120px] w-[120px]" :key="imgIndex" :src="item">
            </img-view>
            <CloseCircleOutlined
              v-if="!props.readOnly"
              class="z-100 absolute right-[-5px] top-[-2px] h-2.5 w-2.5 cursor-pointer"
              @click="deleteImg(key, imgIndex)"
            />
          </div>
        </div>
      </div>
      <div v-if="question.reference_num === 0" class="my-2">
        <a-button
          v-if="
            !props.readOnly &&
            (question.question_type === 1 || question.question_type === 2)
          "
          @click="addOption"
          type="link"
          :icon="h(PlusOutlined)"
          class="pl-0"
          >添加选项</a-button
        >
        <a-button
          v-if="!props.readOnly && question.question_type === 4"
          @click="addBlank"
          type="link"
          :icon="h(PlusOutlined)"
          class="pl-0"
          >添加填空符</a-button
        >
      </div>
      <div
        class="mt-3.75 flex w-full items-center justify-between border-t border-[#ebeef5] pt-2.5"
      >
        <div
          class="flex flex-wrap items-center"
          :class="{ 'w-[80%]': !props.readOnly, 'w-full': props.readOnly }"
        >
          <a-tooltip
            v-if="question.question_type === 5"
            placement="bottomLeft"
            :get-popup-container="getPopupContainer"
            class="w-full"
          >
            <template v-if="question.answer" #title>
              {{ question.answer }}
            </template>
            <span class="line-clamp-3 break-words">
              答案：{{ question.answer }}
            </span>
          </a-tooltip>

          <span v-else>答案：</span>
          <!-- 单选 -->
          <a-radio-group
            v-if="question.question_type === 1"
            v-model:value="question.answer"
            :disabled="props.readOnly"
          >
            <a-radio
              v-for="(value, key, index) in question.content.choice"
              :value="key"
              :key="key"
            >
              {{ String.fromCharCode(64 + index + 1) }}
            </a-radio>
          </a-radio-group>
          <!-- 判断 -->
          <a-radio-group
            v-if="question.question_type === 3"
            v-model:value="question.answer"
            :disabled="readOnly"
          >
            <a-radio
              v-for="(value, key, index) in judgeChoice"
              :value="key"
              :key="key"
            >
              {{ String.fromCharCode(64 + index + 1) }}
            </a-radio>
          </a-radio-group>
          <!-- 多选 -->
          <a-checkbox-group
            v-else-if="question.question_type === 2"
            v-model:value="question.answer"
            :disabled="readOnly"
          >
            <a-checkbox
              v-for="(option, key, index) in question.content.choice"
              :value="key"
              :key="key"
            >
              {{ String.fromCharCode(64 + index + 1) }}
            </a-checkbox>
          </a-checkbox-group>
          <!-- 填空 -->

          <auto-width-input
            v-else-if="question.question_type === 4"
            v-for="(_, index) in question.answer"
            :key="'answer-' + index"
            v-model:value="question.answer[index]"
            class=""
            :readonly="readOnly"
          >
          </auto-width-input>
        </div>
        <div v-if="!isTestManage && !isShowInfo" class="flex">
          <div v-if="!props.readOnly">
            <a-button @click="copyQuestion" size="small" class="mr-2.5"
              >复制</a-button
            >
            <a-tooltip placement="bottom">
              <template #title>删除此题</template>
              <a-button
                :icon="h(DeleteOutlined)"
                size="small"
                @click="deleteQuestion"
              ></a-button>
            </a-tooltip>
          </div>
        </div>
      </div>
      <div class="w-full">
        <div v-if="!props.readOnly">
          <a-popover
            v-if="question.question_type === 5"
            placement="bottom"
            trigger="click"
          >
            <template #content>
              <a-textarea
                :rows="2"
                v-model:value="question.answer"
                class="w-80"
              >
              </a-textarea>
            </template>
            <a-button type="link" class="pl-0">设置答案</a-button>
          </a-popover>
          <a-popover placement="bottom" trigger="click">
            <template #content>
              <template v-if="[1, 2].includes(question.question_type)">
                <div
                  v-for="(value, key, index) in question.content.choice"
                  class="w-80"
                >
                  <span>{{ String.fromCharCode(64 + index + 1) + '.' }}</span>
                  <a-textarea
                    placeholder="请输入解析"
                    :autoSize="{ minRows: 1, maxRows: 2 }"
                    v-model:value="value.analyse"
                  ></a-textarea>
                </div>
              </template>
              <a-textarea
                v-else
                :autoSize="{ minRows: 2 }"
                placeholder="请输入内容"
                v-model:value="question.content.analyse"
                class="mt-2.5 w-80"
              >
              </a-textarea>
            </template>

            <a-button type="link" class="pl-0">设置试题剖析</a-button>
          </a-popover>
          <a-popover placement="bottom" trigger="click">
            <template #content>
              <a-textarea
                :rows="2"
                v-model:value="question.remarks"
                class="w-80"
              ></a-textarea>
            </template>
            <a-button type="link" class="pl-0">设置备注</a-button>
          </a-popover>
        </div>
        <div :class="{ 'mt-1': props.readOnly }">
          <!-- 单选和多选题显示每个选项的解析 -->
          <div v-if="[1, 2].includes(question.question_type) && question.content.choice">
            <a-tooltip
              placement="bottomLeft"
              :get-popup-container="getPopupContainer"
            >
              <template #title>
                <div v-for="(value, key, index) in question.content.choice" :key="key">
                  <div v-if="value.analyse">
                    {{ String.fromCharCode(64 + index + 1) + '.' + value.analyse }}
                  </div>
                </div>
                <div v-if="question.content.analyse">
                  {{ question.content.analyse }}
                </div>
              </template>
              <span class="line-clamp-3 break-words text-[#909399]">
                试题剖析：
                <template v-for="(value, key, index) in question.content.choice" :key="key">
                  <template v-if="value.analyse">
                    {{ String.fromCharCode(64 + index + 1) + '.' + value.analyse }}
                  </template>
                </template>
                <template v-if="question.content.analyse">
                  {{ question.content.analyse }}
                </template>
              </span>
            </a-tooltip>
          </div>

          <!-- 其他题型显示全局解析 -->
          <div v-else>
            <a-tooltip
              placement="bottomLeft"
              :get-popup-container="getPopupContainer"
            >
              <template v-if="question.content.analyse" #title>
                <span> {{ question.content.analyse }}</span>
              </template>
              <span class="line-clamp-3 break-words text-[#909399]"
                >试题剖析： {{ question.content.analyse }}</span
              >
            </a-tooltip>
          </div>

          <a-tooltip placement="bottomLeft">
            <template v-if="question.remarks" #title>
              <span> {{ question.remarks }}</span>
            </template>
            <span class="line-clamp-3 break-words text-[#909399]"
              >备注： {{ question.remarks }}</span
            >
          </a-tooltip>
        </div>
      </div>
    </div>
    <div v-if="isTestManage" class="float-right mt-5">
      <a-button @click="clickCancelBtn" class="mr-5">取 消</a-button>
      <a-button type="primary" @click="clickOkBtn">确 定</a-button>
    </div>
  </a-card>
</template>
