import { defineStore } from 'pinia';
import type { TaskItem,TaskState,TaskStoreActions, TaskConfig } from '#/types/task';
import { getFileExtension } from '@vben/utils';
import { useUserStore } from './user';
/**
 * 存储拉单预览相关组件的公共状态
 */
export const useTaskStore = defineStore<string, TaskState,{
  taskFileExtension: (state: TaskState) => string;
  viewTime: (state: TaskState) => number;
  showHandover: (state: TaskState) => boolean;
  showCannotCheck: (state: TaskState) => boolean;
  isOptV2: (state: TaskState) => boolean;
},TaskStoreActions>('task', {
  state: () => ({
    biz: '',
    queue: '',// 原来的task_name，现在改为queue，更容易理解
    limit:10,
    counter: {
      total: 0,
      load: 0,
      finished: 0,
    },
    disabledManual: true,
    cardClickTime: 0,
    optClickTime: 0,
    highlightClickTimes: 0,
    finishedRenderTime: 0,
    selectedForbidType: undefined,
    isView: false,
    currentQueueConfig: undefined,
    markTexts: [],
    releaseTaskTrigger: false,
    taskItem: undefined as TaskItem |undefined,// 拉单获取的每个任务
    previewConfig: {} as any,
  }),
  actions: {
    setCurrentQueueConfig(value:TaskConfig) {
      this.currentQueueConfig = value;
    },
    setSelectedForbidType(value:number | undefined) {
      if (value === 0) {
        this.selectedForbidType = undefined;
        return;
      }
      // 解析失败，默认切换到10
      if (value === 12) {
        value = 10;
      }
      this.selectedForbidType = value;
    },
    addMarkText(text:string) {
      this.markTexts.push(text);
    },
    clearMarkTexts() {
      this.markTexts = [];
    },
    setCounter(value:{total?:number,load?:number,finished?:number}) {
      if (value.total !== undefined) {
        this.counter.total = value.total;
      }
      if (value.load !== undefined) {
        this.counter.load = value.load;
      }
      if (value.finished !== undefined) {
        this.counter.finished = value.finished;
      }
    },
    resetCounter() {
      this.counter.total = 0;
      this.counter.load = 0;
      this.counter.finished = 0;
    },
    incrementFinished() {
      if (this.counter.finished < this.counter.load) {
        this.counter.finished++;
      }
    },
    setQueue(value:string) {
      this.queue = value;
    },
    setBiz(value:string) {
      this.biz = value;
    },
    setTaskItem(value:TaskItem | undefined) {
      this.taskItem = value;
    },
    resetCardClickTime() {
      this.cardClickTime = Date.now();
    },
    resetOptClickTime() {
      this.optClickTime = Date.now();
    },
    resetFinishedRenderTime() {
      this.finishedRenderTime = Date.now();
    }
  },
  getters: {
    taskFileExtension: (state) => {
      return state.taskItem?.ext||getFileExtension(state.taskItem?.fname || '')||'';
    },
    showHandover(state) {
      return state.currentQueueConfig?.check_tag[0]==="1";
    },
    showCannotCheck(state) {
      return state.currentQueueConfig?.check_tag[1]==="1";
    },
    viewTime(state) {

      const time = state.finishedRenderTime - state.cardClickTime;
      return time > 0 ? time : 30000;
    },
    isOptV2(state) {
      // return true
      return state.currentQueueConfig?.biz === 'drive_core' && ((parseInt(useUserStore().flag || '0') & 0x100) === 0x100);
    }
  },
});
