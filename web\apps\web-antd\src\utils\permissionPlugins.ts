// import { App } from 'vue'
import { usePageStore } from '#/store/page'

// 获取当前路由的权限信息
function getPathPerms(isHeader = false) {
    // 在函数内部调用 usePageStore，确保 Pinia 已经初始化
    const pageStore = usePageStore()

    // header 专用路径，不用路由匹配
    const curPath = isHeader
      ? '/header'
      : location.href.split('#')[1]?.split('?')[0]
    //   : location.href.split('#')[1]?.split('?')[0] || '/'
    const allPerms = pageStore.pagePermissions || []
    const matched = allPerms.find(
      (item: any) =>
        item.path === curPath || item.path + '/' === curPath
    )

    return matched || { op_roles: '' }
  }

  // 按钮级权限判断
  function isShowOpt(optPerm: string, isHeader = false): boolean {
    const perms = getPathPerms(isHeader).op_roles || ''
    console.log("perms",perms);

    return perms.split(',').includes(optPerm)
  }

  // 平台级权限判断
  function isShowPlat(platPerm: string): boolean {
    const perms = getPathPerms(false).platform || ''
    return perms.split(',').includes(platPerm)
  }

export default {
  install(app: any) {
    app.config.globalProperties.$isShowOpt  = isShowOpt
    app.config.globalProperties.$isShowPlat = isShowPlat
  }
}
