// 定义表示范围的类型
type Range = {
  start: {
    row: number;
    column: number;
  };
  end: {
    row: number;
    column: number;
  };
};

// 定义keywords数组中每个元素的类型
type Keyword = {
  ranges: Range[];
  id: number;
  kind: number;
  level: number;
  category: string;
  class: string;
  word: string;
  order: number;
  distance?: number;
  similar: boolean;
  decorate?: string[];
  base_id: number;
  orig_word: string;
};
interface TaskItem {
  index?: number;
  id: number;
  entry: string;
  biz: string;
  fileid: string;
  extra_id: string;
  scene: string;
  type_view: number;
  type_doubt: number;
  priority: number;
  supplier: string;
  ext: string;
  text_type: string;
  content: string;
  image_url: string;
  task_incr: number;
  task_type: number;
  create_time: number;
  modify_time: number;
  pull_time: number;
  pull_create_time: number;
  pull_modify_time: number;
  expire_time: number;
  reviewer_id: number;
  source: string;
  userid: number;
  reason: string;
  render_type: number;
  preview_url?: string;
  task_tag: number;
  aux_key: string;
  extra_json: {
    sentiment_confirm_file_info: Record<string, unknown>;
    related_file_info: Record<string, unknown>;
    pv_info: Record<string, unknown>;
    sandbox_info: SandboxInfo;
    simulate_push_id?: string;
    simulate_pool_id?: string;
    ex_status?: {
      fl: number;
    };
  };
  task_name: string;
  status: string;
  type: number;
  types: number | null;
  level: number;
  in_time: number;
  view_time: number;
  check_time: number;
  buriedPointStatus: number;
  limit: number;
  fname: string;
  uuid: string;
  findex: string;
  exist: boolean;
  pv_count: number;
  uv_count: number;
  gpv_count: number;
  record_flag: number;
  fileinx: string;
  file_url: string;
  fsha: string;
  f_fileid: string;
  f_extra_id: string;
  f_scene: string;
  f_biz: string;
  context: string;
  expired_time: number;
  word: string;
  kind: number;
  base_id: number;
  datetime: string;
  external_json: ExternalData;
  important_comments: any[];
  important_cnt: number;
  high_sen_word: boolean;
  is_illegal_man_word: boolean;
  team: string;
  appeal_cnt: number;
  reviewer_name: string;
  real_name: string;
  now_time: number;
  view_types?: string[];
  fsize: number;
  view_content: string;
  highlight_click_type: string;
  highlight_click_num: number;
  times: number;
  userType: string;
  ai_username: string;
  pull_delay: number;
  manual_cost: number;
  policy_flag: string;
  creator_id: number;
  mark_info: null;
  doc_type: number;
  buttonType?: string; // 按钮类型,驼峰命名是前端添加的变量
  forbidText?: string; // 禁止类型文本
  _frontPayload?: any; // 前端渲染用数据
}
interface IImageTaskItem extends TaskItem {
  picture?: string;
  forbidType?: string;
  imgUrls?: string[];
  borderColor?: string;
}
// 需要扩展的嵌套类型（如果需要独立使用）
interface SandboxInfo {
  task_id: number;
  biz: string;
  scene: string;
  word_info: Record<string, unknown>;
}

interface ExternalData {
  key_num: number;
  keywords: Keyword[];
  ocr: AnalysisResult;
  qr_code: AnalysisResult;
  black_result: null;
  ai_label_desc: string;
}

interface AnalysisResult {
  key_num: number;
  keywords: Keyword[];
}
export interface QueueCountDelayData {
  [key: string]: { [key: string]: {num: number,delay: {forbid: number,preview: number}} };
}
export interface TaskState {
  biz: string;
  queue: string;
  limit: number;
  counter: {
    total: number;
    load: number;
    finished: number;
  };
  disabledManual: boolean;
  cardClickTime: number;
  optClickTime: number;
  finishedRenderTime: number;
  isView: boolean;
  highlightClickTimes: number;
  currentQueueConfig: TaskConfig | undefined;
  taskItem: TaskItem | undefined;
  previewConfig: any;
  selectedForbidType: number | undefined;
  releaseTaskTrigger: boolean;
  markTexts: string[];
}
export interface TaskStoreActions {
  setCounter: (value: { total?: number; load?: number; finished?: number }) => void;
  resetCounter: () => void;
  incrementFinished: () => void;
  setQueue: (value: string) => void;
  setBiz: (value: string) => void;
  setTaskItem: (value: TaskItem | undefined) => void;
  setCurrentQueueConfig: (value: TaskConfig) => void;
  resetCardClickTime: () => void;
  resetOptClickTime: () => void;
  resetFinishedRenderTime: () => void;
  setSelectedForbidType: (value: number | undefined) => void;
  addMarkText: (text: string) => void;
  clearMarkTexts: () => void;
}
export interface TaskConfig {
  id: number;
  biz: string;
  task_name: string;
  task_desc: string;
  task_sql: string;
  type: number;
  type_doubt: number;
  type_view: number;
  source: string;
  supplier: string;
  entry: string;
  ext: string;
  forbid_types: {id: number,name: string,type: number,parent:number,kind:number}[];
  forbid_types_v2: any[];
  check_tag: string;
  image_scale_config: any;
  preview_config: Record<
    string,
    {
      preview_priority: string;
      type_preview: number;
    }
  >;
  pull_config: any;
  scene: string;
}
export interface Label {
  label: string;
  desc: string;
  tag: string;
}

export interface ImageItem {
  bucket: string;
  key: string;
  url: string;
  width: number;
  height: number;
}

export interface HandoverServiceRequest {
  create_time: number;
  modify_time: number;
  pull_time: number;
  task_name: string;
  in_time: number;
  entry: string;
  scene: string;
  uuid: string;
  fileinx: string;
  extra_id: string; // 非drive_core和drice_test时传递
  fileid?: string; // 非drive_core和drice_test时传递
  from: string;
  checkTime: number;
  viewTime: number;
  buriedPointStatus: number;
}

export interface PullTaskRequest {
  biz: string;
  limit: number;
  offset: number;
  entry: string;
  type_view?: number;
}

export interface ActionConfig {
  key: string;
  label: string;
  color: string;
  type: number;
}
export interface KeyboardAction {
  key: string;
  action: string;
}

export type {
  Keyword,
  Range,
  TaskItem,
  IImageTaskItem,
};


