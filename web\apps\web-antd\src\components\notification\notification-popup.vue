<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { message } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { Bell } from '@vben/icons';
import { VbenIconButton, VbenPopover, VbenScrollbar } from '@vben-core/shadcn-ui';
import { getUncheckTip, setMsgRead } from '#/api/account';
import { getPlatformService, getApplyList } from '#/api/platform';
import { formatTimestampToDate } from '#/utils/time';
import { taskRoute, copyToClipboard } from '#/utils/task-route';
import { useTaskStore } from '#/store/task';

interface TipItem {
  msg_type: string;
  msg_id: string;
  create_time: number;
  text: string;
  textInfo?: any;
  platName?: string;
  userid?: string;
  uname?: string;
  reason?: string;
  modify_time?: number;
}

const router = useRouter();
const taskStore = useTaskStore();
const tipItems = ref<TipItem[]>([]);
const msgType = ref('efficiency');
const platList = ref<any[]>([]);
const open = ref(false);

// 消息类型配置
const msgTypeConfig = [
  { key: 'efficiency', label: '时效提醒' },
  { key: 'stuck', label: '任务堆积提醒' },
  { key: 'special_user', label: '特殊用户通知' },
  { key: 'face_verify', label: '人脸确认提醒' },
  { key: 'video_upload', label: '上报视频' },
  { key: 'firewall_approval', label: '防火墙应用审批' },
];

// 计算是否显示红点
const showDot = computed(() => tipItems.value.length > 0);

// 获取平台列表
async function getPlatformList() {
  try {
    const params = { enable: '1' };
    const resultData = await getPlatformService(params);
    platList.value = resultData.data || [];
  } catch (error) {
    console.error('获取平台列表失败:', error);
  }
}

// 获取防火墙申请列表
async function getFirewallApplyList() {
  try {
    const resp = await getApplyList();
    if (resp.result) {
      tipItems.value = resp.data.records.map((item: any) => ({
        ...item,
        msg_type: 'firewall_approval',
        textInfo: '',
      }));
    }
  } catch (error) {
    console.error('获取防火墙申请列表失败:', error);
  }
}

// 获取未读通知数据
async function getUncheckData() {
  try {
    if (msgType.value === 'firewall_approval') {
      await getFirewallApplyList();
      return;
    }

    const res = await getUncheckTip({
      page: 0,
      limit: 100,
      msg_type: msgType.value,
    });

    const resData = res.data.messages || [];
    tipItems.value = resData.map((item: any) => {
      item.textInfo = item.text === '' ? '' : JSON.parse(item.text);
      const matchItem = platList.value.find(
        (pitem) => item.textInfo.biz === pitem.platform
      );
      item.platName = matchItem ? matchItem.detail : '';
      return item;
    });
  } catch (error) {
    console.error('获取通知数据失败:', error);
  }
}

// 处理标签页切换
function handleTabClick(key: string) {
  msgType.value = key;
  getUncheckData();
}

// 跳转到防火墙审批页面
function toFirewallApprovalPage(id: string) {
  router.push({ path: '/platform/app_firewall_approval', query: { id } });
  open.value = false;
}

// 跳转到视频页面
async function toVideoPage(item: TipItem) {
  try {
    const { msg_type, msg_id } = item;
    await setMsgRead({ msg_type, msg_id });
    await getUncheckData();
    router.push({ path: '/platform/tools/record_video', query: { id: item.textInfo.id } });
    open.value = false;
  } catch (error) {
    console.error('跳转视频页面失败:', error);
  }
}

// 跳转到任务页面
async function toTaskPage(item: TipItem) {
  try {
    const { msg_type, msg_id } = item;
    const { task_name, biz } = item.textInfo || {};

    await setMsgRead({ msg_type, msg_id });
    await getUncheckData();

    if (msgType.value === 'face_verify') {
      router.push({ path: '/platform/facial_confirm_list', query: { msg_id } });
    } else if (msgType.value !== 'special_user' && task_name && biz) {
      // 设置当前业务
      taskStore.setBiz(biz);

      // 根据任务名称获取路由名称
      const routeName = taskRoute(task_name);

      // 跳转到对应的任务页面
      router.push({ name: routeName, params: { biz } });
    }
    open.value = false;
  } catch (error) {
    console.error('跳转任务页面失败:', error);
  }
}

// 复制文本到剪贴板
async function handleCopyText(text: string) {
  const success = await copyToClipboard(text);
  if (success) {
    message.success('复制成功');
  } else {
    message.error('复制失败');
  }
}

// 获取消息类型标题
function getMsgTypeTitle(msgType: string) {
  const config = msgTypeConfig.find(item => item.key === msgType);
  return config ? config.label : msgType;
}

// 初始化
onMounted(async () => {
  await getPlatformList();
  await getUncheckData();
});

// 定时刷新通知数据
let refreshTimer: NodeJS.Timeout | null = null;

onMounted(() => {
  // 每30秒刷新一次通知数据
  refreshTimer = setInterval(() => {
    getUncheckData();
  }, 30000);
});

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
  }
});
</script>

<template>
  <VbenPopover v-model:open="open" placement="bottom" :width="450" trigger="click">
    <template #trigger>
      <VbenIconButton class="relative">
        <Bell class="size-5 text-white" />
        <div
          v-if="showDot"
          class="absolute -right-1 -top-1 h-2 w-2 rounded-full bg-red-500"
        />
      </VbenIconButton>
    </template>

    <template #content>
      <div class="w-full">
        <!-- 标签页 -->
        <div class="border-b border-gray-200">
          <div class="flex flex-wrap">
            <button
              v-for="config in msgTypeConfig"
              :key="config.key"
              :class="[
                'px-3 py-2 text-sm font-medium border-b-2 transition-colors',
                msgType === config.key
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              ]"
              @click="handleTabClick(config.key)"
            >
              {{ config.label }}
            </button>
          </div>
        </div>

        <!-- 通知内容 -->
        <VbenScrollbar class="max-h-96">
          <div v-if="tipItems.length > 0" class="p-2">
            <div
              v-for="(item, index) in tipItems"
              :key="index"
              class="mb-3 rounded-lg border border-gray-200 p-3 shadow-sm hover:shadow-md transition-shadow"
            >
              <!-- 标题 -->
              <div class="mb-2 font-bold text-gray-800">
                {{ getMsgTypeTitle(item.msg_type) }}
              </div>

              <!-- 人脸确认提醒 -->
              <div v-if="item.msg_type === 'face_verify'" class="space-y-2">
                <div class="text-sm">
                  user_id：
                  <span class="text-blue-500 cursor-pointer">{{ item.msg_id }}</span>
                </div>
                <button
                  class="text-blue-500 hover:text-blue-700 text-sm"
                  @click="toTaskPage(item)"
                >
                  有新的面部需要确认>>
                </button>
              </div>

              <!-- 录屏上传提醒 -->
              <div v-else-if="item.msg_type === 'video_upload'" class="space-y-2">
                <div class="text-sm">
                  user_id：
                  <span class="text-blue-500">{{ item.textInfo?.user_id }}</span>
                </div>
                <div class="text-sm">
                  备注：
                  <span>{{ item.textInfo?.mark }}</span>
                </div>
                <button
                  class="text-blue-500 hover:text-blue-700 text-sm"
                  @click="toVideoPage(item)"
                >
                  查看视频>>
                </button>
              </div>

              <!-- 防火墙应用审批 -->
              <div v-else-if="item.msg_type === 'firewall_approval'" class="space-y-2">
                <div class="grid grid-cols-2 gap-2 text-sm">
                  <div>用户ID: <span>{{ item.userid }}</span></div>
                  <div>用户昵称: <span>{{ item.uname }}</span></div>
                </div>
                <div class="text-sm">
                  原因: <span>{{ item.reason }}</span>
                </div>
                <div class="text-sm">
                  申请时间: <span>{{ formatTimestampToDate(item.modify_time) }}</span>
                </div>
                <button
                  class="text-blue-500 hover:text-blue-700 text-sm"
                  @click="toFirewallApprovalPage(item.userid)"
                >
                  点击前往审核队列>>
                </button>
              </div>

              <!-- 其他类型通知 -->
              <div v-else class="space-y-2">
                <div class="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    队列：<span class="text-blue-500">{{ item.platName }}</span>
                  </div>
                  <div>
                    状态：
                    <span class="font-bold">
                      {{ item.textInfo?.type_view == 1 ? '先发后审' : '先审后发' }}
                    </span>
                  </div>
                </div>
                <div class="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    入列时间：<span>{{ formatTimestampToDate(item.create_time) }}</span>
                  </div>
                  <div>
                    延时超过: <span>{{ item.textInfo?.expire_second }}s</span>
                  </div>
                </div>
                <div class="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    fileid：
                    <span
                      class="text-blue-500 cursor-pointer"
                      @click="handleCopyText(item.textInfo?.fileid)"
                    >
                      {{ item.textInfo?.fileid }}
                    </span>
                  </div>
                  <div>
                    user_id：
                    <span class="text-blue-500 cursor-pointer">{{ item.textInfo?.user_id }}</span>
                  </div>
                </div>
                <div v-if="item.textInfo?.extra_id" class="text-sm">
                  extra_id：
                  <span class="text-blue-500 cursor-pointer">{{ item.textInfo?.extra_id }}</span>
                </div>
                <div v-if="msgType === 'stuck'" class="text-sm">
                  积压量级: <span>{{ item.textInfo?.count }}</span>
                </div>
                <button
                  class="text-blue-500 hover:text-blue-700 text-sm"
                  @click="toTaskPage(item)"
                >
                  点击前往审核队列>>
                </button>
              </div>
            </div>
          </div>
          <div v-else class="p-4 text-center text-gray-500">
            暂无提醒
          </div>
        </VbenScrollbar>
      </div>
    </template>
  </VbenPopover>
</template>
