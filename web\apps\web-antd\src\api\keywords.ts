import { requestClient } from '#/api/request';
// 关键词提取
export async function getKeywordsExtractList(queryString: string) {
  return requestClient.get(`/manual/keyword_extract/query_word?${queryString}`);
}
// 获取关键词配置列表
export async function getKeywordsWordConfigs(data) {
  return requestClient.get(`/manual/keywords/wordconfigs`,{data});
}

// 获取关键词实验室任务列表
export async function getKeywordsTaskList(data: any) {
  return requestClient.post(`/manual/pre_run/keywords/task/list`, data);
}

// 获取关键词相似词统计
export async function getKeywordsSimilarStatistics(queryString: string) {
  return requestClient.get(
    `/manual/keywords/get_similar_statistics?${queryString}`,
  );
}

// 获取关键词监控数据
export async function getKeywordsMonitor  (data)  {
  return requestClient.get(`/manual/keywords/getmonitor`,{data});
};
// 关键词监控-重算
export async function recalculateKeywords(data: any) {
  return requestClient.post(`/manual/keywords/recalculate`, data);
}
// 关键词监控-账号重算

export async function recalculateNameKeywords(data: any) {
  return requestClient.post(`/manual/keywords/name_recalculate`, data);
}


// 关键词操作日志 select接口
export async function getKeywordsWordLabels() {
  return requestClient.get('/manual/keywords/labels');
}

// 关键词操作日志 表格接口
export async function getKeywordsWordConfigLogs(data) {
  return requestClient.get(`/manual/keywords/wordconfig/logs`,{data});
}

// 关键词命中数据 表格接口
export async function getKeywordsHitData(queryString: string) {
  return requestClient.get(`/manual/keywords/summary?${queryString}`);
}

// 编辑关键词接口
export async function editKeywordsWordConfig(data: any) {
  return requestClient.post('/manual/keywords/wordconfig/edit', data);
}

// 添加关键词接口
export async function addKeywordsWordConfig(data: any) {
  return requestClient.post('/manual/keywords/wordconfig', data);
}

// 删除关键词接口
export async function deleteKeywordsConfigs(data: any) {
  return requestClient.post('/manual/keywords/wordconfigs/delete', data);
}

// 获取词库列表接口
export async function getKeywordsWordBase(queryString?: string) {
  return requestClient.get(
    `/manual/keywords/getbase${queryString ? `?${queryString}` : ''}`,
  );
}

// 添加词库接口
export async function addKeywordsWordBase(data: any) {
  return requestClient.post('/manual/keywords/addbase', data);
}

// 编辑词库接口
export async function editKeywordsWordBase(data: any) {
  return requestClient.post('/manual/keywords/base_reset', data);
}

// 删除词库接口
export async function deleteKeywordsWordBase(queryString: string) {
  return requestClient.get(`/manual/keywords/base_delete?${queryString}`);
}
//词库启用禁用
export function updateKeyordsConfigStatus(data: any) {
  return requestClient.post('/manual/keywords/wordconfigs/status', data);
}
// 获取关键词推审数据
export async function getKeywordsReviewData(queryString: string) {
  return requestClient.get(`/manual/summary/get_word_review?${queryString}`);
}

// 获取关键词误判数据
export async function getKeywordsMisjudgeData(queryString: string) {
  return requestClient.get(
    `/manual/keywords/summary_misjudge_keyword?${queryString}`,
  );
}

// 获取关键词试跑样本配置
export async function getKeywordsSandboxConfig(data) {
  return requestClient.get(`/manual/sandbox/keyword/config`,{data});
}

// 添加编辑关键词试跑样本配置
export async function addEditKeywordsSandboxConfig(data: any) {
  return requestClient.post('/manual/sandbox/keyword/config', data);
}

// 删除关键词试跑样本配置
export async function deleteKeywordsSandboxConfig(data: any) {
  return requestClient.delete('/manual/sandbox/keyword/config', { data });
}

// 试跑样本配置页面, 获取业务列表
export function getProductList(queryString?: string) {
  return requestClient.get(`/manual/control/product_list?${queryString}`);
}

// 获取关键词效果概况
export async function getKeywordsEffectOverview(data) {
  return requestClient.get(`/manual/summary/get_keyword_result`, { data });
}

// 添加关键词实验室任务
export const addKeywordsTask = (data: any) => {
  return requestClient.post('/manual/pre_run/keywords/task/create', data);
};

// 停止关键词实验室任务
export const stopKeywordsTask = (data: any) => {
  return requestClient.post('/manual/pre_run/keywords/task/stop', data);
};

// 获取关键词预跑匹配计数列表
export async function queryKeywordPreRunMatchCountList(data: any) {
  return requestClient.post(`/manual/pre_run/keywords/task/match_count_list?`, data);
}

// 关键词查重
export async function getKwDulplicate(data: any) {
  return requestClient.get(`/manual/keywords/wordconfig/duplicate`, {data});
}

// 关键词相关业务方
export async function getKeywordBiz() {
  return requestClient.get(`/manual/control/get_biz_config`);
}
export async function getKwHit(data: any) {
  return requestClient.post(`/manual/keywords/keywords_hit`, data);
}

// 关键词提取——我的任务
export async function queryMyTaskList(data: any) {
  return requestClient.get(`/manual/keyword_extract/my_task_list`, {data});
}

// 关键词提取——任务概况
export async function queryTask(data: any) {
  return requestClient.get(`/manual/keyword_extract/query_task`, {data});
}

// 关键词提取——任务概况——预跑
export async function testAllTask(data: any) {
  return requestClient.post(`/manual/keyword_extract/test_all_task`, data);
}

export async function queryWord(data: any) {
  return requestClient.get(`/manual/keyword_extract/query_word`, {data});
}
// 关键词实验室详情-命中列表
export async function getMatchFileList(data: any) {
  return requestClient.post(`/manual/pre_run/keywords/task/match_file_list`, data);
}

//创建提词任务
export async function createExtractTask(data: any) {
  return requestClient.post(`/manual/keyword_extract/create_task`, data);
}
