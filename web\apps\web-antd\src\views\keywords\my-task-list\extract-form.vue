<script setup lang="ts">
import { ref, onMounted } from "vue";
import { createExtractTask } from "#/api/keywords";
import { shallowRef } from "vue";

import FormComponent from "#/components/form/index.vue";
import { useUserStore } from "#/store/user";

import { useApi<PERSON>and<PERSON> } from "#/composables/common/use-api-handler";
import ExtractUpload from "./extract-upload.vue";
const userStore = useUserStore();
const { handleApiCall } = useApiHandler();

const formComponentRef = ref(null);

const emit = defineEmits(["success"]);

const commonConfig = {
  labelWidth: 60,
  componentProps: {
    class: "w-full",
  },
};
const file_list = ref([]);
const baseFormSchema = [
  {
    fieldName: "audit_record_list",
    label: "文件信息",
    component: "Textarea",
    componentProps: {
      rows: 4,
      placeholder:
        '["scene1","fileid1","extraid1"];["scene2","fileid2","extraid2"];["scene3","fileid3","extraid3"]',
    },
  },
  {
    fieldName: "file",
    label: "",
    component: shallowRef(ExtractUpload),
    componentProps: {
      onChange: (res) => {
        console.log(res, "xixi");
        let data = res.file;
        if (data.status === "done") {
          console.log(data, "哈哈哈");
          let uploadFileInfo = {
            name: data.name,
            bucket: data.response.data.bucket,
            key: data.response.data.key,
          };
          file_list.value.push(uploadFileInfo);
        }
        if (data.status === "removed") {
          file_list.value = file_list.value.filter((item) => {
            return item.key !== data.response.data.key;
          });
        }
      },
    },
  },
];

const handleFormSubmit = async (values) => {
  let params = {
    audit_record_list: values.audit_record_list,
    file_list: file_list.value,
  };
  await handleApiCall(createExtractTask, params, {
    onSuccess: () => {
      emit("success");
    },
  });
};
</script>

<template>
  <form-component
    :common-config="commonConfig"
    ref="formComponentRef"
    :schema="baseFormSchema"
    @submit="handleFormSubmit"
    :reset-button-options="{ show: false }"
    :submitButtonOptions="{ content: '确定' }"
  >
  </form-component>
</template>
