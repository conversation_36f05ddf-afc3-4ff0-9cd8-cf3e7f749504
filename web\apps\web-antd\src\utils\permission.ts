import type { RouteRecordRaw } from 'vue-router';
// import { filterTree } from '@vben/utils';
// import { useUserStore } from '../store/user';
import { usePageStore } from '../store/page';

// 定义权限项类型
interface PermissionItem {
  path: string;
  op_roles: string;
  [key: string]: any;
}

// 扩展 RouteMeta 类型，添加 parentPermitted 属性
declare module 'vue-router' {
  interface RouteMeta {
    parentPermitted?: boolean;
  }
}

/**
 * 根据用户的 pagePermissions 过滤路由
 * @param routes 路由配置
 * @returns 过滤后的路由
 */
export function filterRoutesByPermission(routes: RouteRecordRaw[]): RouteRecordRaw[] {
  // return routes;
  const pageStore = usePageStore();
  const pagePermissions = pageStore.pagePermissions as PermissionItem[] || [];

  // 创建路径权限集合，方便快速查找
  const permissionPaths = new Set<string>();
  pagePermissions.forEach(perm => {
    permissionPaths.add(perm.path);
  });

  // 检查路径是否有权限（包括父路径检查）
  function checkPathPermission(path: string, parentPath: string = ''): boolean {
    // 处理相对路径
    let fullPath = path;
    if (!path.startsWith('/')) {
      // 如果是相对路径，需要拼接父路径
      fullPath = parentPath ? `${parentPath}/${path}` : `/${path}`;
    }

    // 直接匹配完整路径
    if (permissionPaths.has(fullPath)) {
      return true;
    }

    // 如果是根路径，默认没有权限
    if (fullPath === '/' || fullPath === '') {
      return false;
    }

    // // 检查所有权限路径，看是否有任何一个权限路径是当前路径的前缀
    // for (const permPath of permissionPaths) {
    //   // 如果权限路径是当前路径的前缀，则有权限
    //   if (fullPath.startsWith(permPath)) {
    //     return true;
    //   }
    // }

    return false;
  }

  // 递归处理路由，标记子路由并返回过滤后的路由
  function processRoutes(routes: RouteRecordRaw[], parentPath: string = ''): RouteRecordRaw[] {
    const result: RouteRecordRaw[] = [];

    for (const route of routes) {
      // 构建完整路径
      let currentPath = route.path;
      let fullPath = currentPath;

      if (!currentPath.startsWith('/')) {
        // 相对路径，需要拼接父路径
        fullPath = parentPath ? `${parentPath}/${currentPath}` : `/${currentPath}`;
      } else {
        // 绝对路径，直接使用
        fullPath = currentPath;
      }

      // 检查当前路由是否有权限
      const hasPermission = checkPathPermission(currentPath, parentPath);

      // 处理子路由
      let hasChildWithPermission = false;
      let filteredChildren: RouteRecordRaw[] = [];

      if (route.children && route.children.length > 0) {
        filteredChildren = processRoutes(route.children, fullPath);
        hasChildWithPermission = filteredChildren.length > 0;
      }

      // 如果当前路由有权限或者有子路由有权限，则保留
      if (hasPermission || hasChildWithPermission) {
        // 深度克隆路由对象，确保保留所有属性，包括 meta.order
        const clonedRoute = {
          ...route,
          meta: route.meta ? { ...route.meta } : undefined
        };

        // 如果有子路由，替换为过滤后的子路由
        if (route.children) {
          clonedRoute.children = filteredChildren;
        }

        // 如果当前路由没有权限但子路由有权限，标记为通过父权限获取的权限
        if (!hasPermission && hasChildWithPermission && clonedRoute.meta) {
          clonedRoute.meta.parentPermitted = true;
        }

        result.push(clonedRoute);
      }
    }

    return result;
  }

  // 过滤路由树
  const filteredRoutes = processRoutes(routes);
  return filteredRoutes;
}

/**
 * 检查特定路由是否有权限访问
 * @param path 路由路径
 * @returns 是否有权限访问
 */
export function hasPermissionForPath(path: string): boolean {
  const pageStore = usePageStore();
  const pagePermissions = pageStore.pagePermissions as PermissionItem[] || [];

    // 基本路由，这些路由不需要进入权限拦截
    // 基本路由白名单（如登录、404等）
    const basicPaths = [
      '/404', // 404路由
      '/login', // 登录路由
    ];
    	  // 检查白名单
  if (basicPaths.some(p => p === path)) {
    return true;
  }
  // 获取所有权限路径
  const permissionPaths = new Set<string>();
  pagePermissions.forEach(perm => {
    permissionPaths.add(perm.path);
  });

  // 确保路径格式统一
  let fullPath = path;
  if (!fullPath.startsWith('/')) {
    fullPath = '/' + fullPath;
  }

  // 直接匹配
  if (permissionPaths.has(fullPath)) {
    return true;
  }

  // // 检查是否是子路径或父路径
  // for (const permPath of permissionPaths) {
  //   // 如果权限路径是当前路径的前缀，则有权限（子路径）
  //   if (fullPath.startsWith(permPath)) {
  //     return true;
  //   }

  //   // 如果当前路径是权限路径的前缀，那么当前路径是一个父路由，也应该显示
  //   if (permPath.startsWith(fullPath)) {
  //     return true;
  //   }
  // }

  return false;
}

/**
 * 获取某个路径的操作权限
 * @param path 路由路径
 * @returns 操作权限数组
 */
export function getOperationPermissionsForPath(path: string): string[] {
  const pageStore = usePageStore();
  const pagePermissions = pageStore.pagePermissions as PermissionItem[] || [];

  // 确保路径格式统一
  let fullPath = path;
  if (!fullPath.startsWith('/')) {
    fullPath = '/' + fullPath;
  }

  // 首先尝试精确匹配
  const permItem = pagePermissions.find(perm => perm.path === fullPath);
  if (permItem) {
    return permItem.op_roles.split(',');
  }

  // 如果没有精确匹配，尝试查找最接近的父路径
  let closestParentPath = '';
  let closestParentItem = null;

  for (const perm of pagePermissions) {
    // 如果是父路径，且比当前找到的更长（更具体）
    if (fullPath.startsWith(perm.path) && perm.path.length > closestParentPath.length) {
      closestParentPath = perm.path;
      closestParentItem = perm;
    }
  }

  // 不再查找子路径权限
  // 如果没有找到父路径，尝试查找子路径
  // if (!closestParentItem) {
  //   for (const perm of newpermission) {
  //     if (perm.path.startsWith(fullPath)) {
  //       // 找到了一个子路径，返回它的操作权限
  //       return perm.op_roles.split(',');
  //     }
  //   }
  // }

  return closestParentItem ? closestParentItem.op_roles.split(',') : [];
}
