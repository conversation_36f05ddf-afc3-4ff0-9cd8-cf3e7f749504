// use-nlp.ts
import { defineComponent, h, computed } from 'vue';
import StatusTag from '#/components/tag/index.vue';
import { NLP_MODEL_MAP } from '#/constants/maps/nlp';
import { DOC_MANUAL_STATUS, DOC_MANUAL_COLOR } from '#/constants/maps/status';

// 自定义一个只负责打标题的组件
const NlpTitle = defineComponent({
  name: 'NlpTitle',
  props: { text: { type: String, required: true } },
  setup(props) {
    // 用 render 函数，直接输出一段带样式的文字
    return () =>
      h(
        'div',
        { class: 'mb-2 text-lg font-semibold' },
        props.text
      );
  },
});

export function useNlp() {
  const nlpOptions = computed(() => {
    return (nlpData: any) => {
      if (!nlpData || typeof nlpData !== 'object' || Array.isArray(nlpData)) {
        return [];
      }

      // 先把标题那一项 push 进去
      const opts: any[] = [
        {
          type: 'component',
          component: NlpTitle,
          componentProps: { text: 'NLP模型结果' },
        },
      ];

      Object.keys(nlpData)
        .filter((key) => NLP_MODEL_MAP[key])
        .forEach((key) => {
          opts.push({
            label: NLP_MODEL_MAP[key],
            type: 'component',
            component: StatusTag,
            needOriginDataKeys: ['status'],
            componentProps: {
              status: key,
              tagMap: DOC_MANUAL_STATUS,
              defaultTag: '未操作',
              colorMap: DOC_MANUAL_COLOR,
            },
          });
        });

      return opts;
    };
  });

  return { nlpOptions };
}
