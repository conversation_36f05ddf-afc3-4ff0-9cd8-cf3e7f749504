import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';
import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'ant-design:read-outlined',
      order: 199999,
      title: $t('page.exam.title'),
    },
    name: 'Exam',
    path: '/exam',
    children: [
      {
        name: 'ExamsManage', //考试管理
        path: 'exam_manage',
        component: () => import('#/views/exam/exams-manage.vue'),
        meta: {
          affixTab: true,
          //   icon: 'lucide:area-chart',
          title: $t('page.exam.examsManage'),
        },
      },
      {
        name: 'ExamsList', //试卷列表
        path: 'exam_manage/exam_list',
        component: () => import('#/views/exam/exams-manage/exams-list.vue'),
        meta: {
          hideInMenu: true,
          title: $t('page.exam.examsList'),
        },
      },
      {
        name: 'QuestionsAnalysis', //试题分析
        path: 'exam_manage/questions_analysis',
        component: () =>
          import('#/views/exam/exams-manage/questions-analysis.vue'),
        meta: {
          hideInMenu: true,
          title: $t('page.exam.questionsAnalysis'),
        },
      },
      {
        name: 'AnalysisDetail', //试题分析
        path: 'exam_manage/analysis_detail',
        component: () =>
          import('#/views/exam/exams-manage/analysis-detail.vue'),
        meta: {
          hideInMenu: true,
          title: $t('page.exam.analysisDetail'),
        },
      },
      {
        name: 'PaperCreate', //创建笔试
        path: 'exam_manage/test_create',
        component: () => import('#/views/exam/exams-manage/paper-create.vue'),
        meta: {
          hideInMenu: true,
          title: $t('page.exam.paperCreate'),
        },
      },
      {
        name: 'ExamSituation', //答题情况
        path: 'exam_manage/answer_situation',
        component: () => import('#/views/exam/exams-manage/exam-situation.vue'),
        meta: {
          hideInMenu: true,
          title: $t('page.exam.examSituation'),
        },
      },
      {
        name: 'ExamMark', //试卷批改
        path: 'exam_manage/test_mark',
        component: () => import('#/views/exam/exams-manage/exam-mark.vue'),
        meta: {
          hideInMenu: true,
          title: $t('page.exam.examMark'),
        },
      },
      {
        name: 'MyExams', //我的考试
        path: 'my_exam',
        component: () => import('#/views/exam/my-exams.vue'),
        meta: {
          affixTab: true,
          title: $t('page.exam.myExams'),
        },
      },
      {
        name: 'MyPaper', //考试试卷
        path: 'my_exam/my_paper',
        component: () => import('#/views/exam/my-exam/my-paper.vue'),
        meta: {
          hideInMenu: true,
          title: $t('page.exam.myPaper'),
        },
      },
      {
        name: 'MyAnswerSituation', //答题详情
        path: 'my_exam/paper_answer',
        component: () => import('#/views/exam/my-exam/my-answer-situation.vue'),
        meta: {
          hideInMenu: true,
          title: $t('page.exam.myAnswerSituation'),
        },
      },
      {
        name: 'MyWrongQuestions', //错题详情
        path: 'my_exam/my_wrong_questions',
        component: () => import('#/views/exam/my-exam/my-wrong-questions.vue'),
        meta: {
          hideInMenu: true,
          title: $t('page.exam.myWrongQuestions'),
        },
      },
      {
        name: 'QuestionsManage', //试题管理
        path: 'test_manage',
        component: () => import('#/views/exam/questions-manage.vue'),
        meta: {
          affixTab: true,
          title: $t('page.exam.questionsManage'),
        },
      },
      {
        name: 'Overview',
        path: 'overview',
        component: () => import('#/views/exam/overview.vue'),
        meta: {
          affixTab: true,

          title: $t('page.exam.overview'),
        },
      },
    ],
  },
];

export default routes;
