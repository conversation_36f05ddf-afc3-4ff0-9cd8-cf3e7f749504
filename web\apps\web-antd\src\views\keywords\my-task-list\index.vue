<script lang="ts" setup>
import { ref, type Component } from 'vue';
import TableTemplate from "#/components/table/index.vue";
import { useVbenModal } from "@vben/common-ui";
import StandardFormModal from "#/components/modal/form-modal.vue";
import { useApiHandler } from '#/composables/common/use-api-handler';
import type { VxeGridProps } from '@vben/plugins/vxe-table';
import { queryMyTaskList,createExtractTask } from "#/api/keywords"
import { formatTimestampToDate, transformTimeFields } from "#/utils/time";
import {KEYWORDS_EXTRACT_STATUS_MAP } from "#/constants/maps/keywords";
import ExtractForm from './extract-form.vue';

import { useRouter } from 'vue-router';

const router = useRouter();
interface FormSchema<T extends string = string> {
    component: string | Component;
    fieldName: T;
    label?: string;
    componentProps?: Record<string, any>;
    defaultValue?: any;
    class?: string;
}

const tableTemplateRef = ref<any>(null);

// 分页配置
const paginationOptions = {
    pageSize: 20,
    currentPage: 1,
};

// 表格列配置
const columns: VxeGridProps["columns"] = [
    { title: '序号', field:'id' },
    {
    field: "create_time",
    title: "任务开始时间",
    formatter: ({ cellValue }) => formatTimestampToDate(cellValue),
   },
   {
    field: "finish_time",
    title: "任务完成时间",
    formatter: ({ cellValue }) => formatTimestampToDate(cellValue),
   },
    { title: '失败样本数量', field: 'fail_file_count' },
    { title: '样本数', field: 'success_import_file_count' },
    { title: '提词数', field: 'word_count' },
    { title: '加词数', field: 'add_word_count' },
    {
        title: '状态',
        field: 'status',
        formatter: ({ cellValue }) => KEYWORDS_EXTRACT_STATUS_MAP[cellValue],
    },
    {
        title: '操作',
        field: 'operation',
        slots: { default: 'operation' },
    }
];

// 任务开始时间选择器配置
const rangeStartPickerConfig: FormSchema = {
    component: 'RangePicker', // 使用 RangePicker 组件
    fieldName: 'createTimeRange', // 字段名称为 'createTimeRange'
    label: '任务开始时间', // 标签文本为 '日期'
    class: 'col-span-2',
    componentProps: {
        placeholder: ['请选择开始日期', '请选择结束日期'], // 占位符文本
        format: 'YYYY-MM-DD', // 日期格式
        valueFormat: 'X',
    }
};

// 任务结束时间选择器配置
const rangeEndPickerConfig: FormSchema = {
    component: 'RangePicker', // 使用 RangePicker 组件
    fieldName: 'finishTimeRange', // 字段名称为 'finishTimeRange'
    label: '任务结束', // 标签文本为 '日期'
    class: 'col-span-2',
    componentProps: {
        format: 'YYYY-MM-DD', // 日期格式
        valueFormat: 'X',
    }
};
const schema = [
    rangeStartPickerConfig,
    rangeEndPickerConfig
]
const open = ref(false);


// 搜索配置
const searchOptions = {
    collapsed: false,
    schemas: { '1': schema }, // 直接引用普通数组
    showCollapseButton: false,
    submitOnChange: false,
    wrapperClass: 'grid-cols-8',
};

const openFormModal = (row?: any) => {
  formKey.value++
    open.value = true;
};

const handleOk = async (values: any) => {
  open.value = false;
  tableTemplateRef.value?.refreshTable();
};


// 处理搜索请求
const handleSearch = async ({ page, pageSize, ...formValues }: any) => {
  try {
    // 时间参数转换（核心修改部分）
    const transformedParams = transformTimeFields(
      formValues,
      [
        ["createTimeRange", ["start_create_time", "end_create_time"]], // 任务开始时间范围
        ["finishTimeRange", ["start_finish_time", "end_finish_time"]]  // 任务结束时间范围
      ]
    );

    // 构造请求参数
    const searchParams = {
      ...transformedParams,
      page_no: page - 1,    // 分页参数转换
      page_size: pageSize
    };

    // 调用接口（注意替换为实际接口）
    const response = await queryMyTaskList(searchParams);

    return {
      items: response.data.results || [],
      total: response.data.count || 0
    };
  } catch (error) {
    console.error('获取数据失败:', error);
    return { items: [], total: 0 };
  }
};
const formKey = ref(1);
const view = (row: any) => {
  router.push({
      path: '/keywordExtract/wordList',
      query: {
        taskId: row.id,
      },
    });
}
</script>

<template>
  <div>
    <a-modal v-model:open="open" title="创建提词任务" :footer="null">
      <extract-form :key="formKey" ref="extraFormRef" @success="handleOk" />
    </a-modal>
    <table-template
      ref="tableTemplateRef"
      :columns="columns"
      :pagination-options="paginationOptions"
      :query-method="handleSearch"
      :search-options="searchOptions"
    >
      <template #toolbar-left>
        <a-button type="primary" @click="openFormModal"> 新建任务 </a-button>
      </template>

      <template #operation="{ row }">
        <!-- <a-button @click="openFormModal(row)" type="link">提取</a-button> -->
        <a-button @click="view(row)" type="link">查看</a-button>
      </template>
    </table-template>
  </div>
</template>
