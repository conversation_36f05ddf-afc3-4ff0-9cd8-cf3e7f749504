<script lang="ts" setup>
import { computed, onMounted, onUnmounted, provide, ref, watch } from "vue";
import { useRouter } from "vue-router";

import { IconifyIcon } from "@vben/icons";

import { storeToRefs } from "pinia";

import { getDelayEfficiency, getHourEfficiency } from "#/api/task";
import Field from "#/components/common/Field.vue";
import { useSupplierTasks } from "#/composables/task/use-supplier-tasks";
import { $t } from "#/locales";
import { useTaskStore } from "#/store";
import { EventName, useEventStore } from "#/store/event";
import usePlatformStore from "#/store/platform";
import useWebSocketStore from "#/store/websocket";
import { formatHourToTimeRange, formatSecondsToReadableTime } from "#/utils/time";

const props = withDefaults(
  defineProps<{
    disabledFields?: string[];
    emptyText?: string;
  }>(),
  {
    disabledFields: () => [],
    emptyText: () => $t("page.checkList.task.fileUnload"),
  }
);

const router = useRouter();

const websocketStore = useWebSocketStore();

const eventBus = useEventStore();
const interval = ref<NodeJS.Timeout | null>(null);
const taskStore = useTaskStore();
const { counter } = storeToRefs(taskStore);
const { supplierTasks } = useSupplierTasks(!props.disabledFields.includes("supplier"));
const { currentQueueConfig, queue, biz } = storeToRefs(usePlatformStore());
const userCount = ref({
  show: false,
  date: new Date().toLocaleDateString(),
  count: 0,
  total: 0,
  timeDelay: {
    forbid: 0,
    preview: 0,
  },
  List: [],
});
// 配置用于Field组件的统一值类名
provide("FieldValueClass", "text-red-500");
const now = Date.now();

watch(
  () => websocketStore.queueCountDelayData,
  (newVal) => {
    if (!newVal?.[taskStore.biz]?.[taskStore.queue]?.delay) {
      return;
    }
    const currentDelayData = newVal?.[taskStore.biz]?.[taskStore.queue]?.delay;
    userCount.value.timeDelay.forbid = now / 1000 - (currentDelayData?.forbid ?? 0);
    userCount.value.timeDelay.preview = now / 1000 - (currentDelayData?.preview ?? 0);
  },
  { deep: true }
);
const fetchHourEfficiency = async () => {
  if (
    props.disabledFields.includes("count") &&
    props.disabledFields.includes("total") &&
    props.disabledFields.includes("table")
  ) {
    return;
  }

  if (!biz.value || !queue.value) {
    return;
  }
  const res = await getHourEfficiency({
    biz: biz.value,
    task_name: queue.value,
  });
  userCount.value.count = 0;
  userCount.value.total = 0;
  let list = res.data.hour_efficiency;
  list.forEach((item: any) => {
    // hour等于现在的hour
    if (item.hour === new Date().getHours()) {
      userCount.value.count = item.record_count;
    }
    userCount.value.total += item.record_count;
  });

  list = list.filter((item: any) => {
    if (new Date().getHours() === item.hour) {
      return true;
    } else if (item.record_count === 0 && item.timeout_count === 0) {
      return false;
    }
    return true;
  });
  userCount.value.List = list;
};
const fetchData = async () => {
  await fetchHourEfficiency();
  if (
    props.disabledFields.includes("forbid") ||
    props.disabledFields.includes("preview")
  ) {
    return;
  }
  if (!biz.value || !queue.value) {
    return;
  }
  const delayRes = await getDelayEfficiency({
    biz: biz.value,
    task_name: queue.value,
  });
  userCount.value.timeDelay.forbid = Math.floor(now / 1000 - delayRes.data.forbid);
  userCount.value.timeDelay.preview = Math.floor(now / 1000 - delayRes.data.preview);
};
onMounted(async () => {
  interval.value = setInterval(() => {
    userCount.value.timeDelay.forbid += 1;
    userCount.value.timeDelay.preview += 1;
  }, 1000);
});
onUnmounted(() => {
  if (interval.value) {
    clearInterval(interval.value);
  }
});
const fieldItems = [
  { key: "forbid", label: "紧急延迟" },
  { key: "preview", label: "普通延迟" },
  { key: "date", label: "日期" },
  { key: "count", label: "时量" },
  { key: "total", label: "总量" },
];

const getFieldValue = (key: string) => {
  switch (key) {
    case "count": {
      return userCount.value.count;
    }
    case "date": {
      return new Date().toLocaleDateString();
    }
    case "forbid": {
      return formatSecondsToReadableTime(userCount.value.timeDelay.forbid);
    }
    case "preview": {
      return formatSecondsToReadableTime(userCount.value.timeDelay.preview);
    }
    case "total": {
      return userCount.value.total;
    }
    default: {
      return "";
    }
  }
};
const showSupplier = computed(() => {
  return currentQueueConfig.value?.check_tag
    ? Number(currentQueueConfig.value?.check_tag[6])
    : 0;
});
const toLog = () => {
  if (biz.value === "ciba_account" || biz.value === "wps_account") {
    // 词霸  wps账号跳转  待补充
    router.push({
      path: "/checklist/task/nickname_log",
      query: { biz: biz.value, task_name: queue.value },
    });
  } else if (biz.value === "kdocs_comment") {
    // 全文评论 待补充
    router.push({
      path: "/checklist/task/comment_log",
      query: { biz: biz.value },
    });
  } else {
    // 普通跳转
    router.push({
      path: "/checklist/task/drivecore_log",
      query: { from: biz.value },
    });
  }
};
eventBus.useEvent(EventName.TRIGGER_HOUR_EFFICIENCY, () => {
  fetchHourEfficiency();
});
onMounted(() => {
  console.log("ViewBoard onMounted");
});
watch(
  () => [queue.value, biz.value],
  () => {
    taskStore.resetCounter();
    fetchData();
  }
);
watch(
  () => taskStore.counter,
  () => {
    fetchHourEfficiency();
  },
  { deep: true }
);
defineExpose({
  fetchData,
});
</script>

<template>
  <div class="flex h-11 flex-col justify-between md:flex-row md:items-center">
    <div class="ml-2 mr-2 flex items-center text-nowrap">
      <span v-if="counter.load > 0" class="detail text-sm text-gray-500">
        （待操作文件共 <span class="num text-red-500">{{ counter.total }}</span>
        份， 已加载
        <span class="num text-red-500">{{ counter.load }}</span>
        份， 未处理<span class="num text-red-500">{{
          counter.load - counter.finished
        }}</span>
        份）
      </span>
      <span v-else class="detail text-gray-500">{{ emptyText }}</span>
    </div>

    <div class="flex items-center text-sm text-gray-500">
      <template v-if="!disabledFields.includes('supplier') && showSupplier">
        <field
          v-for="item in supplierTasks"
          :key="item.name"
          :label="item.description"
          :value="item.count"
        />
      </template>
      <div class="ml-2 flex flex-1 text-nowrap">
        <template v-for="item in fieldItems" :key="item.key">
          <field
            v-if="!disabledFields.includes(item.key)"
            :label="item.label"
            :value="getFieldValue(item.key)"
          />
        </template>
      </div>
      <a-dropdown v-if="!disabledFields.includes('table')" class="mr-5">
        <template #overlay>
          <a-menu class="w-96">
            <a-table :data-source="userCount.List" :pagination="false" size="small">
              <a-table-column title="时段" width="120">
                <template #default="{ record }">
                  {{ formatHourToTimeRange(record.hour) }}
                </template>
              </a-table-column>

              <a-table-column
                align="center"
                data-index="record_count"
                title="单数(单)"
                width="80"
              />
              <a-table-column
                align="center"
                data-index="manual_cost"
                title="均效(秒)"
                width="100"
              />
              <a-table-column align="center" title="超时(单)">
                <template #default="{ record }">
                  <span :class="{ 'text-red-500': record.timeout_count > 0 }">{{
                    record.timeout_count
                  }}</span>
                </template>
              </a-table-column>
            </a-table>
          </a-menu>
        </template>
        <a-button size="small">
          <iconify-icon icon="ant-design:down-outlined" />
        </a-button>
      </a-dropdown>

      <a-button
        v-if="!disabledFields.includes('checkLog')"
        class="text-blue-400"
        type="link"
        @click="toLog"
      >
        {{ $t("page.checkList.task.log") }}
      </a-button>
    </div>
  </div>
</template>

<style scoped>
:deep(.ant-table-small) .ant-table-tbody > tr > td {
  padding: 2px 8px;
}
</style>
