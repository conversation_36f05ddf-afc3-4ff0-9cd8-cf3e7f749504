<script setup lang="tsx">
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';

import { useWatermark } from '@vben/hooks';
import { IconifyIcon } from '@vben/icons';

import { Button, message } from 'ant-design-vue';
import { storeToRefs } from 'pinia';

import { applyRemoteTask } from '#/api/download-cloud';
import {
  createFileView,
  createOcrView,
  getThirdView,
  getWebview,
} from '#/api/preview';
import { pushTask } from '#/api/task';
import float from '#/components/common/drag.vue';
import useBurySiteApi from '#/composables/task/use-bury-site-api';
import usePreviewData from '#/composables/task/use-preview-data';
import {
  useLoadingState,
  ViewStatus,
} from '#/composables/ui/use-loading-state';
import { useTaskStore } from '#/store';
import usePlatformStore from '#/store/platform';
import { useUserStore } from '#/store/user';

import KcsView from './KcsView.vue';
import PreviewFailCard from './PreviewFailCard.vue';
import ViewTypeBtns from './ViewTypeBtns.vue';
import WebofficeView from './WebofficeView.vue';

const emit = defineEmits(['loadFail', 'loaded']);
const containerRef = ref<HTMLElement | undefined>(undefined);
const route = useRoute();
const taskStore = useTaskStore();
const userStore = useUserStore();
const platformStore = usePlatformStore();
const { disabledManual, taskItem } = storeToRefs(taskStore);
const {
  fileID,
  extraID,
  scene,
  tag,
  ext,
  url,
  viewTypes,
  viewType,
  biz,
  configViewTypes,
} = usePreviewData();
const { slowPreview } = useBurySiteApi();
// const viewType = ref(1); // 当前预览优先级的值
const {
  status,
  isLoading,
  setLoading,
  setLoaded,
  setLoadFailed,
  isCompleteLoaded,
  setCompleteLoaded,
  reset,
  isLoadFailed,
} = useLoadingState(ViewStatus.UNLOAD);
const webOfficeData = ref<any>({});
const thirdViewUrl = ref('');
const tabIndex = ref(1);
const isTranslate = computed(() => {
  return viewType.value === 6;
});

// 强制刷新标志
const isForce = ref(false);

// kcs预览返回的数据
const kcsTaskResult = ref<FileViewData>({
  url: '',
  url_bucket: '',
  fname: '',
  ocr_url: '',
  extra_url: '',
  fileinx: '',
});

// 添加超时提示相关变量
const timeoutTimer = ref<null | number>(null);
const timeoutMsgKey = 'loading-timeout-tip';

// 添加一个标记变量，表示是否正在通过taskItem变化来更新viewType
const isUpdatingFromTaskItem = ref(false);
// 水印相关代码
const { updateWatermark, updateBlindWatermark } = useWatermark();
const blindWatermarkContainer = ref<HTMLElement | null>(null);
// 判断当前路由是否为Preview
const isPreviewRoute = computed(() => route.name === 'Preview');
// 构建预览参数
const buildPreviewParams = () => {
  const params: Record<string, any> = {
    fileinx: fileID.value,
    scene: scene.value,
    extra_id: extraID.value,
    ext: ext.value || '',
    from: biz.value,
    force: isForce.value,
    is_new: true,
    is_review: true,
    tag: tag.value,
  };
  if ([2, 3].includes(viewType.value)) {
    params.focus = isForce.value;
    params.highlight = viewType.value === 3;
  }
  if ([1, 6].includes(viewType.value)) {
    params.translate = isTranslate.value;
  }
  return params;
};

// 设置WebOffice预览
const setupWebofficeView = async (params: any) => {
  try {
    const resp = await getWebview(params);
    if (!resp || !resp.result) {
      setLoadFailed();
      return false;
    }
    webOfficeData.value = resp;
    setLoaded();
    return true;
  } catch {
    setLoadFailed();
    return false;
  }
};

// 设置第三方预览
const setupThirdView = async (params: any) => {
  try {
    const resp = await getThirdView(params);
    if (!resp || !resp.result) {
      setLoadFailed();
      return false;
    }
    thirdViewUrl.value = resp.data.url;
    setLoaded();
    return true;
  } catch {
    setLoadFailed();
    return false;
  }
};

// 设置KCS预览
const setupKcsView = async (params: any) => {
  try {
    // 文件预览请求
    const resp = await createFileView(params);
    if (!resp || !resp.result) {
      setLoadFailed();
      return false;
    }

    // OCR预览请求
    const ocrResp = await createOcrView(params);
    if (!ocrResp || !ocrResp.result) {
      setLoadFailed();
      return false;
    }

    // 合并预览数据
    kcsTaskResult.value = resp.data;
    kcsTaskResult.value.ocr_url = ocrResp.data.ocr_url;
    setLoaded();
    return true;
  } catch {
    setLoadFailed();
    return false;
  }
};

// 处理URL直接预览
const handleUrlPreview = (previewUrl: string) => {
  if (previewUrl) {
    thirdViewUrl.value = previewUrl;
    setLoaded();
    return true;
  }
  return false;
};

const downloadFile = async () => {
  try {
    const resp = await applyRemoteTask({
      fileid: taskItem.value?.fileinx,
      extra_id: taskItem.value?.extra_id,
      scene: taskItem.value?.scene,
      biz: biz.value,
    });
    if (resp.result) {
      taskStore.disabledManual = false;
      if (resp.status_code === 101) {
        message.warning('下载失败~');
      } else {
        message.success('下载任务已提交');
      }
    } else {
      message.warning('下载任务失败');
    }
  } catch {
    message.warning('下载任务失败');
  }
};
// 清除超时计时器
const clearTimeoutTimer = () => {
  if (timeoutTimer.value !== null) {
    window.clearTimeout(timeoutTimer.value);
    timeoutTimer.value = null;
    message.destroy(timeoutMsgKey); // 清除显示的消息
  }
};
// 开始超时计时器
const startTimeoutTimer = () => {
  if (!platformStore.isDownloadShow) {
    return;
  }
  clearTimeoutTimer();
  timeoutTimer.value = window.setTimeout(() => {
    message.warning({
      content: (
        <span>
          <span>当前资源文件过大，加载可能较慢，请耐心等待</span>
          <span>，或者</span>
          <Button class="m-0 px-0" onClick={downloadFile} type="link">
            下载到云主机
          </Button>
          查看
          <span
            class="ml-1 inline-block cursor-pointer"
            onClick={() => message.destroy(timeoutMsgKey)}
          >
            <IconifyIcon icon="ri:close-circle-line" />
          </span>
        </span>
      ),
      key: timeoutMsgKey,
      duration: 0, // 不自动关闭
    });
    // 再过20秒（总共30秒）后显示加载失败
    timeoutTimer.value = window.setTimeout(() => {
      setLoadFailed();
      message.destroy(timeoutMsgKey); // 清除之前的提示消息
    }, 20_000);
  }, 10_000); // 10秒后显示提示
};

/**
 * 获取文件预览数据
 * @returns {Promise<void>} 预览加载的Promise
 */
const getFileViewData = async () => {
  if (!fileID.value) {
    console.log('getFileViewData1', fileID.value);
    setLoadFailed();
    return;
  }
  setLoading();
  startTimeoutTimer(); // 开始计时
  // 构建预览参数
  const params = buildPreviewParams();

  try {
    // 根据不同预览类型处理
    switch (viewType.value) {
      case 2:
      case 3: {
        await setupWebofficeView(params);
        break;
      }
      case 4: {
        await setupThirdView(params);
        break;
      }
      case 5: {
        // 直接URL预览
        if (url.value && handleUrlPreview(url.value)) {
          break;
        }
        // 如果没有preview_url，fallback到标准KCS预览
        // await setupKcsView(params);
        setLoadFailed();
        break;
      }
      default: {
        // 默认KCS预览 (viewType为1或6)
        await setupKcsView(params);
        break;
      }
    }
  } catch (error) {
    console.error('预览加载失败', error);
    setLoadFailed();
    throw error;
  }
};
/**
 * 更新水印
 * 根据当前用户和时间生成水印内容
 */
const updateWatermarks = async () => {
  if (isPreviewRoute.value) {
    const currentDate = new Date();
    const formattedDate = `${currentDate.getFullYear()}-${currentDate.getMonth() + 1}-${currentDate.getDate()}`;
    const realName = userStore.realname;

    // 更新普通水印
    await updateWatermark({
      fontColor: 'gray',
      content: `${formattedDate}\n${realName}`,
    });

    // 更新盲水印
    if (blindWatermarkContainer.value) {
      await updateBlindWatermark({
        fontColor: '#000',
        content: `${formattedDate}\n${realName}`,
        parent: blindWatermarkContainer.value,
        globalAlpha: 100,
      });
    }
  }
};

const bbsMsgHandler = async (event: MessageEvent) => {
  // 叠楼监听来自home.wps.cn的数据
  const jiayuanHost = 'https://bbs.wps.cn';
  // 判断源路径是否来自预期发生者
  if (event.origin.includes(jiayuanHost)) {
    // 获取传过来的数据
    // eslint-disable-next-line no-console
    console.log(event.data, '看下叠楼给我发送了什么数据');
    const floorData = event.data;
    if (!floorData.fileid) return;
    const o = {
      reviewer_id: userStore.userid,
      status: Number(floorData.type) ? 'forbid' : 'ok',
      biz: floorData.biz,
      fileid: `${floorData.fileid}`,
      entry: 'taskGloble',
      extra_id: `${floorData.extra_id}`,
      scene: floorData.scene,
      is_illegal_man_word: floorData.is_illegal_man_word,
      kind: floorData.kind,
      task_tag: taskItem.value?.task_tag,
      aux_key: taskItem.value?.aux_key,
      extra_json: taskItem.value?.extra_json,
    };

    if (Number(floorData.type)) {
      Object.assign(o, { type: Number(floorData.type) });
    }
    const floolRes = await pushTask(o);

    if (!floolRes || !floolRes.result) {
      message.error('操作失败');
      return;
    }
    message.success('操作成功');
    // 再传回去一条消息
    event.source?.postMessage(floorData.type, { targetOrigin: jiayuanHost });
  }
};
const refresh = () => {
  console.log('refresh');
  // 设置强制刷新标志
  isForce.value = true;
  setLoading();
  // 执行预览加载
  getFileViewData().finally(() => {
    // 请求完成后重置强制刷新标志
    isForce.value = false;
  });
};
// 修改viewType的监听
watch(viewType, () => {
  disabledManual.value = true;
  setLoading();
  // 只有不是通过taskItem更新时才调用getFileViewData
  if (!isUpdatingFromTaskItem.value) {
    getFileViewData();
  }
});
// 修改taskItem的监听
watch(
  () => taskStore.taskItem,
  (newTaskItem) => {
    if (newTaskItem) {
      isUpdatingFromTaskItem.value = true; // 设置标记
      configViewTypes();
      viewType.value = viewTypes.value[0] || 1;
      getFileViewData();
      // 使用nextTick确保在DOM更新后再重置标记
      nextTick(() => {
        isUpdatingFromTaskItem.value = false;
      });
    } else {
      reset();
      clearTimeoutTimer(); // 任务变化时清除计时器
    }
  },
);

// 监听路由参数变化
watch(
  () => route.query.fileinx,
  (newFileinx) => {
    if (newFileinx && !taskStore.taskItem) {
      getFileViewData();
    }
  },
);

watch(
  () => status.value,
  (newStatus) => {
    if (isCompleteLoaded.value) {
      emit('loaded', viewType.value);
      clearTimeoutTimer(); // 加载成功时清除计时器
    } else if (newStatus === ViewStatus.LOAD_FAIL) {
      emit('loadFail', viewType.value);
      taskStore.disabledManual = true;
      clearTimeoutTimer(); // 加载失败时清除计时器
    }
  },
);

// 监听viewTypes变化
watch(
  viewTypes,
  (newTypes) => {
    if (newTypes && Array.isArray(newTypes) && newTypes.length === 0) {
      configViewTypes();
      viewType.value = viewTypes.value[0] || 1;
    }
  },
  { immediate: true },
);

// 组件挂载时，检查数据源并初始化
onMounted(() => {
  console.log('onMounted', taskStore.taskItem, viewType.value);
  // 如果viewTypes为空，尝试重新配置
  if (viewTypes.value.length === 0) {
    configViewTypes();
    viewType.value = viewTypes.value[0] || 1;
  }
  getFileViewData();
  // 初始化水印
  updateWatermarks();

  // 监听叠楼消息
  window.addEventListener('message', bbsMsgHandler);
});

// 组件卸载时清除计时器
onUnmounted(() => {
  clearTimeoutTimer();
  // 移除叠楼消息监听
  window.removeEventListener('message', bbsMsgHandler);
});

// 暴露接口给父组件
defineExpose({
  getFileViewData,
  // configViewTypes,
  refresh,
});
</script>

<template>
  <div ref="containerRef">
    <preview-fail-card
      v-if="isLoadFailed"
      :is-translate="isTranslate"
      @after-download="taskStore.disabledManual = false"
    />
    <div
      v-if="status !== ViewStatus.UNLOAD && status !== ViewStatus.LOAD_FAIL"
      class="flex h-full w-full flex-col"
      v-loading="{ active: isLoading, text: '拼命加载中...' }"
    >
      <a-button
        class="absolute right-0 top-2 z-20 mr-2 flex items-center"
        danger
        @click="slowPreview(viewType)"
      >
        {{ $t('page.checkList.task.lagReport') }}
        <template #icon>
          <iconify-icon icon="ri:slow-down-line" />
        </template>
      </a-button>
      <!-- kcs预览方式 -->
      <kcs-view
        v-if="viewType === 1 || viewType === 6"
        :key="tabIndex"
        :data="kcsTaskResult"
        @loaded="setCompleteLoaded"
      />
      <!-- weboffice预览方式 -->
      <weboffice-view
        v-if="viewType === 2 || viewType === 3"
        :data="webOfficeData"
        :highlight="viewType === 3"
        class="h-full"
        @error="setLoadFailed"
        @open="setCompleteLoaded"
      />
      <!-- 第三方预览方式 -->
      <iframe
        v-if="viewType === 4 || viewType === 5"
        :src="thirdViewUrl"
        class="h-full"
        @load="setCompleteLoaded"
      ></iframe>
    </div>
    <float
      v-if="containerRef"
      :parent-element="containerRef"
      position-base="bottom-right"
    >
      <view-type-btns
        v-show="status !== ViewStatus.UNLOAD"
        v-model:value="viewType"
        :view-types="viewTypes"
        @refresh="refresh"
      />
    </float>
    <!-- 盲水印容器 -->
    <div ref="blindWatermarkContainer" class="blind-watermark-container"></div>
  </div>
</template>
