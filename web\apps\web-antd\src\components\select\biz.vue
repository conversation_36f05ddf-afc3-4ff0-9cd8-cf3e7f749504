<script lang="ts" setup>
import { ApiComponent } from "@vben/common-ui";

import { useQuery } from "@tanstack/vue-query";
import { Select } from "ant-design-vue";

import { useBizInfo } from "#/composables/common/use-biz";

const props = withDefaults(
  defineProps<{
    data?: any[];
    immediate?: boolean;
    isAI?: boolean;
    modelValue?: any; // 接收 v-model 绑定的值
  }>(),
  {
    immediate: false,
    isAI: false,
    modelValue: undefined,
    data: undefined,
  }
);
// 定义 emits，用于更新 v-model
const emit = defineEmits(["update:modelValue"]);
/**
 * 生成平台列表
 * @param data 平台数据
 * @returns 生成的平台列表
 */
const { bizList, fetchData } = useBizInfo();
// 使用 useQuery 获取数据（只请求一次）
const { data: apiData, isSuccess } = useQuery({
  queryKey: ["bizData"],
  queryFn: () => fetchData(props.isAI),
  // select: (res) => res.data.data || []
});

/**
 * 获取下拉选项数据
 */
async function fetchApi() {
  if (props.data) {
    return props.data;
  }
  // 确保数据已加载成功
  if (isSuccess.value && apiData.value) {
    return apiData.value;
  }

  await fetchData(props.isAI);
  return bizList.value;
}
/**
 * 处理 Select 的值变化
 */
function handleChange(value: any) {
  emit("update:modelValue", value); // 触发 v-model 更新
}

const filterOption = (input: string, option: any) => {
  return option.label.toLowerCase().includes(input.toLowerCase());
};
</script>

<template>
  <api-component
    :api="fetchApi"
    :component="Select"
    :filter-option="filterOption"
    :immediate="immediate"
    :model-value="modelValue"
    :value="modelValue"
    loading-slot="suffixIcon"
    show-search
    visible-event="onDropdownVisibleChange"
    @change="handleChange"
  />
</template>
