/* 这是键词模块路由文件 */
import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';
import { $t } from '#/locales';

// 导入所有组件
import AllWordList from '#/views/keywords/all-word-list/index.vue';
import Lexicon from '#/views/keywords/lexicon/index.vue';
import Query from '#/views/keywords/query/index.vue';
import Add from '#/views/keywords/add/index.vue';
import QueryRepeat from '#/views/keywords/query-repeat/index.vue';
import SimilarStatistics from '#/views/keywords/similar-sta/index.vue';
import KeywordControll from '#/views/keywords/keyword-controll/index.vue';
import KeywordData from '#/views/keywords/keyword-data/index.vue';
import KeywordLaboratory from '#/views/keywords/keyword-laboratory/index.vue';
import KeywordLaboratoryDetail from '#/views/keywords/keyword-laboratory/detail.vue';
import KeywordLaboratoryDetailList from '#/views/keywords/keyword-laboratory/list.vue';
import RunningSetting from '#/views/keywords/running-setting/index.vue';
import KeywordList from '#/views/keywords/keyword-list/index.vue';
import KeywordAudit from '#/views/keywords/keyword-audit/index.vue';
import KeywordTarget from '#/views/keywords/keyword-controll/detail.vue';
import MyTaskList from '#/views/keywords/my-task-list/index.vue';
import AllTaskList from '#/views/keywords/all-task-list/index.vue';
import WordList from '#/views/keywords/word-list/index.vue';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'ant-design:highlight-outlined',
      order: 0,
      title: $t('page.keywordExtract.title'),
    },
    name: 'KeywordExtract',
    path: '/keywordExtract',
    children: [
      {
        name: 'myTaskList',
        path: 'myTaskList',
        component: MyTaskList,
        meta: {
          title: $t('page.keywordExtract.task.myTaskList'),
        },
      },
      {
        name: 'allTaskList',
        path: 'allTaskList',
        component: AllTaskList,
        meta: {
          title: $t('page.keywordExtract.task.allTaskList'),
        },
      },
      {
        name: 'AllWordList',
        path: 'allWordList',
        component: AllWordList,
        meta: {
          title: $t('page.keywordExtract.task.allWordList'),
        },
      },
      {
        name: 'WordList',
        path: 'wordList',
        component: WordList,
        meta: {
          hideInMenu: true,
          title: $t('page.keywordExtract.task.WordList'),
        },
      },
    ],
  },
  {
    component: BasicLayout,
    meta: {
      icon: 'ant-design:key-outlined',
      order: 0,
      title: $t('page.keywords.title'),
    },
    name: 'Keywords',
    path: '/keywords',
    children: [
      {
        name: 'lexicon',
        path: 'lexicon',
        component: Lexicon,
        meta: {
          affixTab: true,
          title: $t('page.keywords.task.lexicon'),
        },
      },
      {
        name: 'query',
        path: 'query',
        component: Query,
        meta: {
          affixTab: true,
          title: $t('page.keywords.task.query'),
        },
      },
      {
        name: 'add',
        path: 'add',
        component: Add,
        meta: {
          affixTab: true,
          title: $t('page.keywords.task.add'),
        },
      },
      {
        name: 'queryRepeat',
        path: 'query_repeat',
        component: QueryRepeat,
        meta: {
          affixTab: true,
          title: $t('page.keywords.task.query_repeat'),
        },
      },
      {
        name: 'similarStatistics',
        path: 'similar_sta',
        component: SimilarStatistics,
        meta: {
          affixTab: true,
          title: $t('page.keywords.task.similar_sta'),
        },
      },
      {
        name: 'keywordControll',
        path: 'keyword_controll',
        component: KeywordControll,
        meta: {
          affixTab: true,
          title: $t('page.keywords.task.keyword_controll'),
        },
      },
      {
        name: 'keywordTarget',
        path: 'keyword_target',
        component: KeywordTarget,
        meta: {
          hideInMenu: true,
          title: $t('page.keywords.task.keyword_target'),
        },
      },
      {
        name: 'keywordData',
        path: 'keyword_data',
        component: KeywordData,
        meta: {
          affixTab: true,
          title: $t('page.keywords.task.keyword_data'),
        },
      },
      {
        name: 'keywordLaboratory',
        path: 'keyword_laboratory',
        component: KeywordLaboratory,
        meta: {
          affixTab: true,
          title: $t('page.keywords.task.keyword_laboratory'),
        },
      },
      {
        name: 'keywordLaboratoryDetail',
        path: 'keyword_laboratory/detail',
        component: KeywordLaboratoryDetail,
        meta: {
          hideInMenu: true,
          title: '关键词实验室详情',
        },
      },
      {
        name: 'keywordLaboratoryDetailList',
        path: 'keyword_laboratory/detail/list',
        component: KeywordLaboratoryDetailList,
        meta: {
          hideInMenu: true,
          title: '命中列表',
        },
      },
      {
        name: 'runningSetting',
        path: 'running_setting',
        component: RunningSetting,
        meta: {
          affixTab: true,
          title: $t('page.keywords.task.running_setting'),
        },
      },
      {
        name: 'keywordList',
        path: 'keyword_list',
        component: KeywordList,
        meta: {
          affixTab: true,
          title: $t('page.keywords.task.keyword_list'),
        },
      },
      {
        name: 'keywordAudit',
        path: 'keyword_audit',
        component: KeywordAudit,
        meta: {
          affixTab: true,
          title: $t('page.keywords.task.keyword_audit'),
        },
      },
    ],
  },
];

export default routes;
