<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import { getApplyList } from '#/api/platform';
import { formatTimestampToDate } from '#/utils/time';

const route = useRoute();
const loading = ref(false);
const dataSource = ref<any[]>([]);
const selectedUserId = ref<string>('');

// 表格列配置
const columns = [
  {
    title: '用户ID',
    dataIndex: 'userid',
    key: 'userid',
  },
  {
    title: '用户昵称',
    dataIndex: 'uname',
    key: 'uname',
  },
  {
    title: '申请原因',
    dataIndex: 'reason',
    key: 'reason',
  },
  {
    title: '申请时间',
    dataIndex: 'modify_time',
    key: 'modify_time',
    customRender: ({ text }: { text: number }) => formatTimestampToDate(text),
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
  },
];

// 获取申请列表
async function fetchApplyList() {
  try {
    loading.value = true;
    const resp = await getApplyList();
    if (resp.result) {
      dataSource.value = resp.data.records || [];
      
      // 如果有指定的用户ID，高亮显示
      if (route.query.id) {
        selectedUserId.value = route.query.id as string;
      }
    } else {
      message.error('获取申请列表失败');
    }
  } catch (error) {
    console.error('获取申请列表失败:', error);
    message.error('获取申请列表失败');
  } finally {
    loading.value = false;
  }
}

// 审批操作
function handleApprove(record: any) {
  message.success(`已批准用户 ${record.uname} 的申请`);
  // 这里应该调用审批API
  console.log('批准申请:', record);
}

// 拒绝操作
function handleReject(record: any) {
  message.success(`已拒绝用户 ${record.uname} 的申请`);
  // 这里应该调用拒绝API
  console.log('拒绝申请:', record);
}

// 查看详情
function handleViewDetail(record: any) {
  message.info(`查看用户 ${record.uname} 的详细信息`);
  // 这里可以打开详情弹窗或跳转到详情页面
  console.log('查看详情:', record);
}

// 行样式
function getRowClass(record: any) {
  return record.userid === selectedUserId.value ? 'bg-blue-50' : '';
}

onMounted(() => {
  fetchApplyList();
});
</script>

<template>
  <div class="p-6">
    <div class="mb-4">
      <h1 class="text-2xl font-bold text-gray-800">防火墙应用审批</h1>
      <p class="text-gray-600 mt-2">管理和审批用户的防火墙应用申请</p>
    </div>

    <div class="bg-white rounded-lg shadow">
      <div class="p-4 border-b border-gray-200">
        <div class="flex justify-between items-center">
          <h2 class="text-lg font-semibold text-gray-800">申请列表</h2>
          <a-button @click="fetchApplyList" :loading="loading">
            刷新
          </a-button>
        </div>
      </div>

      <a-table
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="{ pageSize: 10, showSizeChanger: true, showQuickJumper: true }"
        :row-class-name="getRowClass"
        row-key="userid"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button
                type="primary"
                size="small"
                @click="handleApprove(record)"
              >
                批准
              </a-button>
              <a-button
                danger
                size="small"
                @click="handleReject(record)"
              >
                拒绝
              </a-button>
              <a-button
                type="link"
                size="small"
                @click="handleViewDetail(record)"
              >
                详情
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 如果有指定的用户ID，显示提示信息 -->
    <a-alert
      v-if="selectedUserId"
      :message="`当前查看用户ID: ${selectedUserId} 的申请`"
      type="info"
      show-icon
      class="mt-4"
    />
  </div>
</template>

<style scoped>
.bg-blue-50 {
  background-color: #eff6ff;
}
</style>
