<script setup lang="ts">
import { ref } from "vue";
import { useRoute } from "vue-router";
import {
  getLastNDaysRange,
  transformTimeFields,
  formatTimestampToDate,
} from "#/utils/time";
import TableTemplate from "#/components/table/index.vue";
import StatusTag from "#/components/tag/index.vue";
import { getTextLogs } from "#/api/operateLog";
import { OPERATE_MANUAL_STAUTS, DOC_MANUAL_COLOR } from "#/constants/maps/status";
const route = useRoute();
import { TEXT_FORBID_LABEL_MAP } from "#/constants/maps/task";
const searchOptions = {
  schemas: {
    "1": [
      {
        component: "RangePicker",
        fieldName: "dateRange",
        label: "日期范围",
        defaultValue: getLastNDaysRange(0, 0, "X"),
        class: "col-span-2",
        componentProps: {
          format: "YYYY-MM-DD HH:mm:ss",
          valueFormat: "X",
        },
      },
    ],
  },
  showCollapseButton: true,
  submitOnChange: false,
  wrapperClass: "grid-cols-6",
};
const columns: VxeGridProps["columns"] = [
  {
    type: "seq",
    align: "center",
    title: "序号",
    fixed: "left",
    width: 100,
  },
  {
    field: "userid",
    title: "userID",
  },
  {
    field: "content",
    title: "昵称",
  },
  {
    title: "状态",
    slots: { default: "status" },
  },
  {
    title: "类型",
    formatter: ({ row }) => {
      return row.reviewer_name == "ai" ? "-" : TEXT_FORBID_LABEL_MAP[row.type];
    },
  },
  {
    title: "机审时间",
    formatter: ({ row }) => {
      return formatTimestampToDate(row.create_time);
    },
  },
  {
    title: "拉单时间",
    formatter: ({ row }) => {
      return formatTimestampToDate(row.create_time + parseInt(row.delay_time || "0"));
    },
  },
  {
    title: "操作时间",
    formatter: ({ row }) => {
      return formatTimestampToDate(row.modify_time);
    },
  },
];
// 分页配置
const paginationOptions = ref({
  pageSize: 20,
  currentPage: 1,
  layouts: ["PrevPage", "NextPage"],
});
const getData = async ({
  page,
  pageSize,
  ...searchValues
}: {
  page: number;
  pageSize: number;
  [key: string]: any;
}) => {
  try {
    let params: any = {
      page: page - 1,
      size: pageSize,
      from: route.query.biz,
      ...transformTimeFields(searchValues, [["dateRange", ["start_time", "end_time"]]]),
    };
    const res = await getTextLogs(params);
    let items = res.data.logs || [];
    return { items, total: res?.data?.logs.length == pageSize ? 1000000 : 0 };
  } catch (error) {
    return { items: [], total: 0 };
  }
};
</script>
<template>
  <div>
    <table-template
      ref="tableTemplateRef"
      :columns="columns"
      :search-options="searchOptions"
      :pagination-options="paginationOptions"
      :query-method="getData"
    >
      <template #status="{ row }">
        <status-tag
          :status="row.status"
          default-tag="未操作"
          :tag-map="OPERATE_MANUAL_STAUTS"
          :color-map="DOC_MANUAL_COLOR"
        />
      </template>
    </table-template>
  </div>
</template>
