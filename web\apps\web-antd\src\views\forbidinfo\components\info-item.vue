<script setup lang="ts">
import { IconifyIcon } from '@vben/icons';
import { isAllEmptyValues } from '#/utils/data';
import { computed } from 'vue';
// 添加 props 定义
const props = defineProps<{
    itemData?: any,
    data: any,
    componentPropsData: any,
}>();
const showItem = computed(() => {
    // 一些不展示item的逻辑 待优化
    if ((props.itemData.type === 'text' || props.itemData.type === 'function') && props.data == undefined) return false;
    if (props.itemData.type === 'component') {
        if (props.itemData.ifShowKey) {
            return props.itemData.ifShowKey.every(
                (sItem: any) => props.componentPropsData[sItem]
            );
        }
        return !isAllEmptyValues(props.componentPropsData);
    }
    return true;
})
</script>
<template>
    <div v-if="showItem">
        <strong v-if="itemData.label">{{ itemData.label }}:</strong>
        <a-tooltip placement="topLeft" :title="itemData?.tooltip" v-if="itemData?.tooltip">
            <span class="inline-flex items-center">
                <IconifyIcon icon="ant-design:question-circle-outlined" height="15" width="15" color="#666" />
            </span>
        </a-tooltip>
        <!-- 普通文本类型 -->
        <span v-if="itemData.type === 'text'">
            {{ data && data !== '无' ? data : '无' }}
        </span>
        <!-- 映射文本类型 -->
        <span v-else-if="itemData.type === 'text-map'">
            {{ itemData.maps[data[0]] || '无' }}
        </span>
        <!-- 组件类型 -->
        <component v-else-if="itemData.type === 'component'" :is="itemData.component" v-bind="componentPropsData" />
        <!-- 函数类型 -->
        <span v-else-if="itemData.type === 'function'">
            {{ itemData.formatFunction(data) || '无' }}
        </span>
        <!-- 链接类型 -->
        <span v-else-if="itemData.type === 'link'">
            <a v-if="data" :href="data" target="_blank" class="text-blue-600 hover:underline">
                {{ itemData.linkText }}
            </a>
            <span v-else>无</span>
        </span>
    </div>
</template>
