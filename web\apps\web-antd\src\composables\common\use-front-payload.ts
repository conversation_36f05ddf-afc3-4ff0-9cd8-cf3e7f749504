import type { Ref } from "vue";
import { isRef } from "vue";

/**
 * 定义带有_frontPayload属性的对象类型
 */
interface WithFrontPayload {
  _frontPayload?: Record<string, any>;
}
export const setPayload = (target: Ref<any> | any, key: string, value: any) => {
  // 判断是否为响应式对象
  if (isRef(target)) {
   const targetValue = target.value as WithFrontPayload;
   if (targetValue._frontPayload) {
     targetValue._frontPayload[key] = value;
   } else {
     targetValue._frontPayload = {
       [key]: value,
     };
   }
   return targetValue;
 } else {
   // 非响应式对象处理
   const targetObj = target as WithFrontPayload;
   if (targetObj._frontPayload) {
     targetObj._frontPayload[key] = value;
   } else {
     targetObj._frontPayload = {
       [key]: value,
     };
   }
   return targetObj;
 }
};

export const payload = (target: Ref<any> | any, key: string) => {
  if (isRef(target)) {
    return (target.value as WithFrontPayload)._frontPayload?.[key];
  } else {
    return (target as WithFrontPayload)._frontPayload?.[key];
  }
};
/**
 * 前端数据载荷管理工具
 * @param target 目标对象，可以是响应式或非响应式变量
 * @returns 包含setPayload和payload的方法对象
 * 
 * @example
 * // 响应式变量示例
 * const form = ref({});
 * const { setPayload, payload } = useFrontPayload(form);
 * setPayload('key1', 'value1');
 * console.log(payload('key1')); // 输出: value1
 * 
 * // 非响应式变量示例
 * const data = {};
 * const { setPayload, payload } = useFrontPayload(data);
 * setPayload('key1', 'value1');
 * console.log(payload('key1')); // 输出: value1
 */
export const useFrontPayload = <T extends Record<string, any>>(target: Ref<T> | T) => {
  // 设置payload数据
  const setPayload0 = (key: string, value: any) => {
    setPayload(target, key, value);
  };

  // 获取payload数据
  const payload0 = (key: string) => {
    return payload(target, key);
  };

  return {
    setPayload: setPayload0,
    payload: payload0,
  };
};

