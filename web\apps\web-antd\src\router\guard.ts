import type { Router, RouteRecordRaw } from 'vue-router';

import { DEFAULT_HOME_PATH, LOGIN_PATH } from '@vben/constants';
import { preferences } from '@vben/preferences';
import { useAccessStore } from '@vben/stores';
import { startProgress, stopProgress } from '@vben/utils';
import type { MenuRecordRaw } from '@vben/types';
import { useUserStore } from '#/store/user'
import { accessRoutes, coreRouteNames } from '#/router/routes';
import { filterRoutesByPermission, hasPermissionForPath } from '#/utils/permission';

import { generateAccess } from './access';

/**
 * 通用守卫配置
 * @param router
 */
function setupCommonGuard(router: Router) {
  // 记录已经加载的页面
  const loadedPaths = new Set<string>();
  router.beforeEach(async (to) => {
    to.meta.loaded = loadedPaths.has(to.path);
    // 页面加载进度条
    if (!to.meta.loaded && preferences.transition.progress) {
      startProgress();
    }
    return true;
  });

  router.afterEach((to) => {
    // 记录页面是否加载,如果已经加载，后续的页面切换动画等效果不在重复执行

    loadedPaths.add(to.path);

    // 关闭页面加载进度条
    if (preferences.transition.progress) {
      stopProgress();
    }
  });
}

/**
 * 权限访问守卫配置
 * @param router
 */
function setupAccessGuard(router: Router) {
  router.beforeEach(async (to, from) => {
    const accessStore = useAccessStore();

    // 基本路由，这些路由不需要进入权限拦截
    if (coreRouteNames.includes(to.name as string)) {
      if (to.path === LOGIN_PATH && accessStore.accessToken) {
        return decodeURIComponent(
          (to.query?.redirect as string) || DEFAULT_HOME_PATH,
        );
      }
      return true;
    }

    // accessToken 检查
    if (!accessStore.accessToken) {
      // 明确声明忽略权限访问权限，则可以访问
      if (to.meta.ignoreAccess) {
        return true;
      }

      // 没有访问权限，跳转登录页面
      if (to.fullPath !== LOGIN_PATH) {
        return {
          path: LOGIN_PATH,
          // // 如不需要，直接删除 query
          // query: { redirect: encodeURIComponent(to.fullPath) },
          // // 携带当前跳转的页面，登录后重新跳转该页面
          // replace: true,
        };
      }
      return to;
    }

    // 是否已经生成过动态路由
    if (accessStore.isAccessChecked) {
      // 在这里添加对已登录用户访问无权限路由的处理
      // 检查当前访问的路由是否有权限
      if (!to.matched || to.matched.length === 0) {
        // 如果路由不匹配，已经是404了，直接放行
        return {
          path: '/404',
          replace: true
        };
      }

      const { path } = to;
      // 使用 hasPermissionForPath 检查用户是否有权限访问当前路径
      const hasPermission = hasPermissionForPath(path);
      if (!hasPermission && !to.meta.ignoreAccess) {
        // 重定向到404页面
        return {
          path: '/404',
          replace: true
        };
      }
      return true;
    }
    // 首先基于角色生成菜单和路由
    const { accessibleRoutes } = await generateAccess({
      router,
      // 则会在菜单中显示，但是访问会被重定向到403
      routes: accessRoutes,
    });
    // 基于 newpermission 再次过滤路由
    const permissionFilteredRoutes = filterRoutesByPermission(accessibleRoutes);
    // 从过滤后的路由构建菜单
    const permissionFilteredMenus = buildMenuFromRoutes(permissionFilteredRoutes);
    // 保存菜单信息和路由信息
    accessStore.setAccessMenus(permissionFilteredMenus);
    accessStore.setAccessRoutes(permissionFilteredRoutes);
    accessStore.setIsAccessChecked(true);
    const redirectPath = (from.query.redirect ?? to.fullPath) as string;

    let finalPath = decodeURIComponent(redirectPath);

    // 检查用户是否有权限访问默认首页
    const hasDefaultHomePathPermission = hasPermissionForPath(DEFAULT_HOME_PATH);

    // 如果当前路径是默认首页，但用户没有权限访问
    if (finalPath === DEFAULT_HOME_PATH && !hasDefaultHomePathPermission) {
      // 找到第一个有效菜单项
      for (const menu of permissionFilteredMenus) {
        // 如果有子菜单，则使用第一个子菜单
        if (menu.children && menu.children.length > 0) {
          for (const subMenu of menu.children as MenuRecordRaw[]) {
            if (subMenu.path && subMenu.path !== '/' && subMenu.path !== DEFAULT_HOME_PATH) {
              finalPath = subMenu.path;
              console.log("跳转到第一个子菜单:", finalPath);
              break;
            }
          }
          if (finalPath !== DEFAULT_HOME_PATH) {
            break;
          }
        }
        if (menu.path && menu.path !== '/' && menu.path !== DEFAULT_HOME_PATH) {
          finalPath = menu.path;
          console.log("跳转到第一个菜单:", finalPath);
          break;
        }
      }
    }
    return {
      ...router.resolve(finalPath),
      replace: true,
    };
  });
}

/**
 * 项目守卫配置
 * @param router
 */
function createRouterGuard(router: Router) {
  /** 通用 */
  setupCommonGuard(router);
  /** 权限访问 */
  setupAccessGuard(router);
}

/**
 * 将路由转换为菜单
 * @param routes 路由配置
 * @returns 菜单配置
 */
function buildMenuFromRoutes(routes: RouteRecordRaw[]): MenuRecordRaw[] {
  // 递归构建菜单
  function buildMenu(routes: RouteRecordRaw[], parentPath: string = ''): MenuRecordRaw[] {
    const result: MenuRecordRaw[] = [];

    for (const route of routes) {
      // 过滤掉 hideInMenu: true 的路由
      if (route.meta?.hideInMenu === true) {
        continue; // 跳过此路由，不添加到菜单中
      }

      // 构建完整路径
      let currentPath = route.path;
      let fullPath = currentPath;

      if (!currentPath.startsWith('/')) {
        // 相对路径，需要拼接父路径
        fullPath = parentPath ? `${parentPath}/${currentPath}` : `/${currentPath}`;
      }

      // 基本菜单属性
      const menu: MenuRecordRaw = {
        path: fullPath, // 使用完整路径
        name: route.meta?.title as string || route.name as string,
        children: [], // 先初始化为空数组
      };

      // 处理子路由，并过滤掉 hideInMenu 的子路由
      if (route.children && route.children.length > 0) {
        menu.children = buildMenu(route.children, fullPath);
      }

      // 添加图标属性
      if (route.meta?.icon) {
        menu.icon = route.meta.icon as string;
      }

      // 添加活动图标属性
      if (route.meta?.activeIcon) {
        menu.activeIcon = route.meta.activeIcon as string;
      }

      // 添加徽章属性
      if (route.meta?.badge) {
        if (typeof route.meta.badge === 'function') {
          menu.badge = route.meta.badge as () => string;
        } else {
          menu.badge = route.meta.badge as string | number;
        }
      }

      // 添加徽章类型
      if (route.meta?.badgeType) {
        menu.badgeType = route.meta.badgeType as 'dot' | 'normal';
      }

      // 添加排序
      if (route.meta?.order !== undefined) {
        menu.order = route.meta.order as number;
      }

      // 如果当前菜单有子菜单或者自身应该显示，则添加到结果中
      if ((menu.children && menu.children.length > 0) || !route.meta?.hideInMenu) {
        result.push(menu);
      }
    }

    // 对菜单进行排序
    result.sort((a, b) => (a.order || 999) - (b.order || 999));

    // 添加调试日志，查看菜单排序
    if (import.meta.env.DEV) {
      console.log('菜单排序后:', result.map(item => ({
        name: item.name,
        path: item.path,
        order: item.order
      })));
    }

    return result;
  }

  return buildMenu(routes);
}

export { createRouterGuard };
