<script setup lang="ts">
import type { BaseFormComponentType } from '@vben/common-ui';

import type { VxeGridProps } from '#/adapter/vxe-table';
import type { FormSchema } from '#/types/form';

import { onBeforeMount, ref, shallowRef } from 'vue';

import { deleteKeywordsSandboxConfig } from '#/api/keywords';
import { supplierRegisterService } from '#/api/platform';
import { getQualitySettingService } from '#/api/quality';
import FormComponent from '#/components/form/index.vue';
import ProductSelect from '#/components/select/product.vue';
import QueueSelect from '#/components/select/queue.vue';
import SupplierSelect from '#/components/select/supplier.vue';
import UserSelect from '#/components/select/user.vue';
import TableTemplate from '#/components/table/index.vue';
import { useApiHandler } from '#/composables/common/use-api-handler';
import { QUALITY_MODULE_TYPE_MAP } from '#/constants/maps/statistics';
import { FORBID_FIELD_CONFIGS } from '#/constants/options';
import { mapToOptions } from '#/utils/transform';

import ForbidType from './components/forbid-type.vue';
import SampleStatus from './components/sample-status.vue';

const { handleApiCall } = useApiHandler();

// 表格组件引用
const tableTemplateRef = ref<any>(null);
// 控制编辑模态框的显示
const isEditModalVisible = ref<boolean>(false);
// 当前编辑的行数据
const currentEditRow = ref<any>(null);
const teamMap = ref<Record<string, string>>({});
// 状态类型映射

const columns: VxeGridProps['columns'] = [
  { type: 'seq', width: 60, title: '序号' },
  { field: 'biz_name', title: '业务名称', align: 'center' },
  {
    field: 'module_type',
    title: '模块类型',
    align: 'center',
    formatter: ({ cellValue }) => {
      const value = cellValue || '';
      return QUALITY_MODULE_TYPE_MAP[value] || '';
    },
  },
  {
    field: 'supplier',
    title: '团队',
    align: 'center',
    formatter: ({ cellValue }) => {
      return teamMap.value[cellValue] || '';
    },
  },
  {
    fixed: 'right',
    title: '操作',
    width: 350,
    slots: { default: 'action' },
  },
];

// 表单组件引用
const formRef = ref<any>(null);
const commonConfig = {
  labelWidth: 80,
  componentProps: {
    class: 'w-full',
  },
};
const schema: FormSchema<BaseFormComponentType>[] = [
  {
    fieldName: 'module_type',
    component: 'Select',
    formItemClass: 'col-span-6',
    componentProps: {
      class: 'w-full',
      placeholder: '请选择模块',
      options: mapToOptions(QUALITY_MODULE_TYPE_MAP),
    },
    label: '模块',
    rules: 'required',
  },
  {
    fieldName: 'biz',
    formItemClass: 'col-span-6',
    component: shallowRef(ProductSelect),
    componentProps: {
      class: 'w-full',
      productLineType: 'quality_platform',
      placeholder: '请选择业务编号',
      modelPropName: 'value',
      immediate: true,
    },
    label: '业务编号',
    rules: 'required',
  },
  {
    fieldName: 'supplier',
    formItemClass: 'col-span-6',
    component: shallowRef(SupplierSelect),
    componentProps: {
      class: 'w-full',
      placeholder: '请选择团队',
      immediate: true,
    },
    label: '团队',
  },
  {
    fieldName: 'time',
    label: '时间',
    component: 'RangePicker',
    formItemClass: 'col-span-6',
    componentProps: {
      showTime: true,
      valueFormat: 'X',
      placeholder: ['开始日期', '结束日期'],
    },
    rules: 'required',
  },
  {
    fieldName: 'sample_status',
    formItemClass: 'col-span-6',
    label: '样本状态',
    component: shallowRef(SampleStatus),
    componentProps: {
      defaultStatus: 1,
      isFormField: true,
      onChange: (values: any) => {
        console.log('values', values);
      },
    },
  },
  {
    fieldName: 'sample_random_percent',
    component: 'Hidden',
    defaultValue: 0,
  },
  {
    fieldName: 'sample_ok_percent',
    component: 'Hidden',
    defaultValue: 0,
  },
  {
    fieldName: 'sample_forbid_percent',
    component: 'Hidden',
    defaultValue: 0,
  },
  {
    fieldName: 'forbid_type',
    formItemClass: 'col-span-6 items-start',
    label: '违规类型',
    component: shallowRef(ForbidType),
    componentProps: {
      defaultType: 1,
      isFormField: true,
      onChange: (values: any) => {
        console.log('forbid type values', values);
      },
    },
  },
  {
    component: shallowRef(QueueSelect),
    fieldName: 'origin_entries',
    formItemClass: 'col-span-6',
    label: '工单来源',
    componentProps: {
      placeholder: '请选择工单来源',
      immediate: true,
      multiple: true,
    },
    dependencies: {
      componentProps(values: any) {
        console.log('values', values.biz);
        return {
          biz: values.biz, // 动态更新 options
        };
      },
      triggerFields: ['biz'],
    },
    rules: 'required',
  },
  {
    fieldName: 'man_access',
    formItemClass: 'col-span-6',
    label: '进审类型',
    component: 'Select',
    componentProps: {
      class: 'w-full',
      options: [
        { value: '', label: '全部' },
        { value: 'preview_forbid', label: '先审后发' },
        { value: 'preview_ok', label: '先发后审' },
      ],
    },
  },
  {
    component: shallowRef(UserSelect),
    fieldName: 'user',
    formItemClass: 'col-span-6',
    label: '操作角色',
    componentProps: {
      placeholder: '请选择操作角色',
      class: 'w-full',
    },
    rules: 'required',
  },
];
const getKeywordOpLogs = async ({
  page,
  pageSize,
  ...formValues
}: {
  [key: string]: any;
  page: number;
  pageSize: number;
}) => {
  try {
    const baseParams = {
      page: page - 1,
      limit: pageSize,
      ...formValues,
    };
    const baseResponseData = await getQualitySettingService(baseParams);
    return {
      items: baseResponseData.data || [],
      total: baseResponseData.data.length || 0,
    };
  } catch {
    return {
      items: [],
      total: 0,
    };
  }
};

const paginationOptions = {
  pageSize: 20,
  currentPage: 1,
};
const isEdit = ref(false);

// 处理添加
const handleAdd = () => {
  // 设置为添加模式
  isEdit.value = false;
  currentEditRow.value = null;
  isEditModalVisible.value = true;
  // 弹窗显示后重置表单
  setTimeout(() => {
    if (formRef.value && formRef.value.formApi) {
      formRef.value.formApi.resetForm();
    }
  }, 0);
};

// 处理编辑
const handleEdit = (row: any) => {
  // 设置为编辑模式
  isEdit.value = true;
  currentEditRow.value = { ...row };
  console.log('currentEditRow', currentEditRow.value);
  isEditModalVisible.value = true;

  // 延长等待时间，确保异步组件加载完成
  setTimeout(() => {
    if (formRef.value && formRef.value.formApi) {
      // 确定样本状态值
      const sampleStatus =
        currentEditRow.value.status_random_percent > 0 ? 1 : 2;

      // 确定违规类型配置
      const isRandomType = currentEditRow.value.type_random_percent > 0;
      const forbidTypeValue = {
        type: isRandomType ? 1 : 2,
        randomPercent: currentEditRow.value.type_random_percent || 0,
        percentages: {} as Record<string, number>,
      };

      // 填充违规类型百分比数据
      FORBID_FIELD_CONFIGS.forEach(({ field }) => {
        forbidTypeValue.percentages[field] = currentEditRow.value[field] || 0;
      });

      // 格式化数据以适应表单组件
      const formattedData = {
        ...currentEditRow.value,
        module_type: String(currentEditRow.value.module_type || ''),
        // 转换时间字段格式
        time:
          currentEditRow.value.start_time && currentEditRow.value.end_time
            ? [currentEditRow.value.start_time, currentEditRow.value.end_time]
            : undefined,
        // 样本状态处理
        sample_status: {
          status: sampleStatus,
          randomPercent: currentEditRow.value.status_random_percent || 0,
          okPercent: currentEditRow.value.status_ok_percent || 0,
          forbidPercent: currentEditRow.value.status_forbid_percent || 0,
        },
        // 违规类型处理
        forbid_type: forbidTypeValue,
        // 其他百分比
        sample_random_percent: currentEditRow.value.status_random_percent || 0,
        sample_ok_percent: currentEditRow.value.status_ok_percent || 0,
        sample_forbid_percent: currentEditRow.value.status_forbid_percent || 0,
      };
      console.log('回填数据:', formattedData);
      formRef.value.formApi.setValues(formattedData);
    }
  }, 500);
};

// 处理删除
const handleDelete = async (id: string) => {
  await handleApiCall(
    deleteKeywordsSandboxConfig,
    { config_id: id },
    {
      onSuccess: () => {
        tableTemplateRef.value?.refreshTable();
      },
    },
  );
};

onBeforeMount(async () => {
  const resp = await supplierRegisterService();
  resp.data.register_list.forEach((item: any) => {
    teamMap.value[item.supplier] = item.mark;
  });
  teamMap.value.custom = '自定义团队';
  console.log('resp', resp);
});
// 模态框保存成功后刷新表格数据
const handleSaveSuccess = () => {
  isEditModalVisible.value = false;
  tableTemplateRef.value?.refreshTable();
};

// 添加保存表单处理函数
const handleFormSubmit = async (values: any) => {
  console.log('表单提交数据:', values);
  // 这里可以添加保存逻辑
  // TODO: 调用保存API
  handleSaveSuccess();
};
</script>

<template>
  <div>
    <table-template
      ref="tableTemplateRef"
      :columns="columns"
      :pagination-options="paginationOptions"
      :query-method="getKeywordOpLogs"
    >
      <template #action="{ row }">
        <a-button type="link" @click="handleEdit(row)">编辑</a-button>
        <a-button type="link" @click="handleEdit(row)">生成样本</a-button>
        <a-button danger type="link" @click="handleEdit(row)">
          重置队列
        </a-button>
        <a-popconfirm
          cancel-text="取消"
          ok-text="确定"
          title="确定删除？"
          @confirm="handleDelete(row.id)"
        >
          <a-button danger type="link">删除</a-button>
        </a-popconfirm>
      </template>
      <template #toolbar-left>
        <a-button type="primary" @click="handleAdd">添加</a-button>
      </template>
    </table-template>

    <a-modal
      v-model:open="isEditModalVisible"
      :footer="null"
      :width="700"
      title="质检样本配置"
    >
      <form-component
        ref="formRef"
        :common-config="commonConfig"
        :schema="schema"
        action-wrapper-class="grid-cols-6"
        wrapper-class="grid-cols-6"
        @submit="handleFormSubmit"
      />
    </a-modal>
  </div>
</template>
