<script setup lang="ts">
import type { VxeGridProps } from "#/adapter/vxe-table";
import { ref, onMounted, shallowRef, computed } from "vue";
import TableTemplate from "#/components/table/index.vue";
import LibrarySelect from "#/components/select/library.vue";
import CategorySelect from "#/components/select/category.vue";
import KeywordsForm from "#/views/keywords/components/keywords-form.vue";
import { useApiHandler } from "#/composables/common/use-api-handler";
const { loadings, handleApiCall } = useApiHandler();

import {
  getKeywordsWordConfigs,
  deleteKeywordsConfigs,
  updateKeyordsConfigStatus,
} from "#/api/keywords";
import { findLabelByValue } from "#/utils/data";
import {
  KEYWORD_KIND_MAP,
  KEYWORD_STATUS_MAP,
  KEYWORD_STRUCT_MAP,
  KEYWORD_SORT_TYPE_MAP,
  KEYWORD_SOURCE_MAP,
  ORDER_LIST,
} from "#/constants/maps/keywords";
import { formatTimestampToDate, transformTimeFields } from "#/utils/time";
import { mapToOptions, generateUrlParams } from "#/utils/transform";
import { useKeywordCategory } from "#/composables/keyword/use-category";
import { useKeywordLibary } from "#/composables/keyword/use-libary";
import { useUserStore } from "#/store";
const userStore = useUserStore();

// 获取分类数据和分类树数据
const {
  categoryTreeData,
  categoryFlattenData,
  fetchData: loadCategoryData,
} = useKeywordCategory();
const { libraryData, fetchData: loadLibaryData } = useKeywordLibary(true);

// 表格组件引用
const tableTemplateRef = ref<any>(null);

// 控制编辑模态框的显示
const isEditModalVisible = ref<boolean>(false);
// 当前编辑的行数据
const currentEditRow = ref<any>(null);

const getLibaryName = (value: string) => {
  return findLabelByValue(libraryData.value, value);
};
const getLabelDesc = (value: string) => {
  return findLabelByValue(categoryFlattenData.value, value, "label", "desc");
};

onMounted(() => {
  loadLibaryData(); // 词库
  loadCategoryData(); // 类别
});

// 搜索选项
const searchOptions = {
  collapsed: false,
  schemas: {
    "1": [
      {
        component: shallowRef(LibrarySelect),
        fieldName: "base_id",
        label: "词库",
        componentProps: {
          apiData: libraryData,
          modelPropName: "value",
        },
      },
      {
        component: "Input",
        fieldName: "word",
        label: "关键词",
      },
      {
        component: "Select",
        fieldName: "kind",
        label: "级别",
        componentProps: {
          options: mapToOptions(KEYWORD_KIND_MAP),
        },
      },
      {
        component: shallowRef(CategorySelect),
        fieldName: "type",
        label: "类别",
        class: "max-w-60 w-full",
        componentProps(values: any) {
          return {
            treeData: categoryTreeData,
            changeOnSelect: true,
          };
        },
      },
      {
        component: "Select",
        fieldName: "status",
        label: "状态",
        componentProps: {
          options: mapToOptions(KEYWORD_STATUS_MAP),
          placeholder: "请选择",
          allowClear: true,
        },
      },
      {
        component: "Input",
        fieldName: "username",
        label: "操作账号",
        componentProps: {
          placeholder: "请输入",
        },
      },
      {
        component: "Select",
        fieldName: "word_struct",
        label: "关键词结构",
        defaultValue: "all",
        componentProps: {
          options: mapToOptions(KEYWORD_STRUCT_MAP),
          placeholder: "请选择",
          allowClear: true,
        },
      },
      {
        component: "Input",
        fieldName: "char_num",
        label: "字数",
        componentProps: {
          placeholder: "请输入",
        },
      },
      {
        component: "Select",
        fieldName: "sort_by",
        label: "排序方式",
        defaultValue: "timeDesc",
        componentProps: {
          options: mapToOptions(KEYWORD_SORT_TYPE_MAP),
          placeholder: "请选择",
          allowClear: true,
        },
      },
      {
        component: "Select",
        fieldName: "mark",
        label: "来源",
        componentProps: {
          options: mapToOptions(KEYWORD_SOURCE_MAP),
          placeholder: "请选择",
          allowClear: true,
        },
      },
      {
        component: "RangePicker",
        fieldName: "time",
        label: "时间",
        class: "col-span-2",
        componentProps: {
          showTime: true,
          valueFormat: "X",
          placeholder: ["开始时间", "结束时间"],
        },
      },
    ],
  },
  showCollapseButton: true,
  submitOnChange: false,
  wrapperClass: "grid-cols-6",
};

const selectedItems = computed(() => {
  return tableTemplateRef?.value?.getTableSelectedItems() || [];
});
const selectedIds = computed(() => {
  return selectedItems.value.map((item: any) => item.id);
});
const hasDisabled = computed(() => {
  return selectedItems.value.some((item: any) => item.status === "disable");
});

const columns: VxeGridProps["columns"] = [
  { type: "checkbox", width: 50, align: "center", fixed: "left" },
  { type: "seq", width: 60, title: "序号" },
  { field: "id", title: "ID", visible: false },
  {
    field: "base_id",
    title: "词库",
    formatter: ({ cellValue }) => getLibaryName(cellValue),
  },
  {
    field: "word",
    title: "主词",
    formatter: ({ row }) => (row.status == "disable" ? `${row.word}(已禁用)` : row.word),
  },
  { field: "extra_word", title: "副词" },
  { field: "similar", title: "相似词" },
  {
    field: "kind",
    title: "级别",
    width: 80,
    formatter: ({ cellValue }) => {
      return KEYWORD_KIND_MAP[cellValue] || "";
    },
  },
  {
    field: "level",
    title: "一级分类",
    formatter: ({ cellValue }) => getLabelDesc(cellValue),
  },
  {
    field: "category",
    title: "二级分类",
    formatter: ({ cellValue }) => getLabelDesc(cellValue),
  },
  {
    field: "class",
    title: "三级分类",
    formatter: ({ cellValue }) => getLabelDesc(cellValue),
  },
  {
    field: "distance",
    title: "偏移距离",
    formatter: ({ cellValue }) => cellValue || "-",
  },
  {
    field: "order",
    title: "副词前后置",
    formatter: ({ cellValue }) => ORDER_LIST[cellValue] || "-",
  },
  {
    field: "effect_time",
    title: "生效日期",
    formatter: ({ cellValue }) => formatTimestampToDate(cellValue),
  },
  {
    field: "expire_time",
    title: "过期日期",
    formatter: ({ cellValue }) => formatTimestampToDate(cellValue),
  },
  {
    field: "mark",
    title: "来源",
    formatter: ({ cellValue }) => cellValue || "其他",
  },
  {
    field: "status",
    title: "状态",
    formatter: ({ cellValue }) => (cellValue === "enable" ? "启用" : "禁用"),
  },
  {
    field: "modify_time",
    title: "操作时间",
    formatter: ({ cellValue }) => formatTimestampToDate(cellValue),
  },
  {
    field: "username",
    title: "操作账号",
  },
  {
    field: "action",
    fixed: "right",
    title: "操作",
    width: 200,
    slots: { default: "action" },
  },
];
const solveParams = (page, pageSize, formValues: any) => {
  let params = {
    page: page - 1,
    limit: pageSize,
    level: formValues.type?.[0],
    category: formValues.type?.[1],
    class: formValues.type?.[2],
    ...transformTimeFields(formValues, [["time", ["start_time", "end_time"]]]),
  };
  delete params.type;
  return params;
};
const getKeywordConfigs = async ({
  page,
  pageSize,
  ...formValues
}: {
  page: number;
  pageSize: number;
  [key: string]: any;
}) => {
  try {
    let params = solveParams(page, pageSize, formValues);
    const responseData = await getKeywordsWordConfigs(params);
    return {
      items: responseData?.data?.words || [],
      total: responseData?.data?.count || 0,
    };
  } catch (error) {
    return {
      items: [],
      total: 0,
    };
  }
};

const paginationOptions = {
  pageSize: 20,
  currentPage: 1,
};

const handleEdit = (row: any) => {
  // 设置当前编辑行数据
  currentEditRow.value = { ...row };
  // 显示编辑模态框
  isEditModalVisible.value = true;
};

const handleDelete = async (row: any) => {
  let ids = row ? [row.id] : selectedIds.value;
  let params = {
    ids: ids,
  };
  await handleApiCall(deleteKeywordsConfigs, params, {
    loadingKey: "batchDelete",
    onSuccess: () => {
      tableTemplateRef.value?.refreshTable();
    },
  });
};
const handleStatus = async (hasDisabled) => {
  let params = {
    ids: selectedIds.value,
    status: hasDisabled ? "enable" : "disable",
  };
  await handleApiCall(updateKeyordsConfigStatus, params, {
    loadingKey: "enableStatus",
    onSuccess: () => {
      tableTemplateRef.value?.refreshTable();
    },
  });
};
// 模态框保存成功后刷新表格数据
const handleSaveSuccess = () => {
  isEditModalVisible.value = false;
  tableTemplateRef.value?.refreshTable();
};
const delComfirmText = "确定要删除所选关键词吗？";
const handleExport = async () => {
  let searchData = await tableTemplateRef.value.getFormvalues();
  let searchParams = solveParams(1, 20, searchData);
  // 构造需要传递的参数对象
  const params = {
    user_id: userStore.userid,
    ...searchParams,
  };

  const url = `/manual/export/wordconfigs${generateUrlParams(params)}`;

  window.location.href = url;
};
</script>

<template>
  <div>
    <table-template
      ref="tableTemplateRef"
      :row-class-name="({ row }) => (row.status == 'disable' ? 'text-red-500' : '')"
      :columns="columns"
      :pagination-options="paginationOptions"
      :query-method="getKeywordConfigs"
      :search-options="searchOptions"
      :has-export="true"
      @export="handleExport"
    >
      <template #toolbar-left>
        <a-popconfirm
          :title="`确定要${hasDisabled ? '启用' : '禁用'}关键词吗？`"
          @confirm="handleStatus(hasDisabled)"
        >
          <a-button
            type="primary"
            class="mr-2"
            :loading="loadings.enableStatus"
            :disabled="!selectedItems.length"
          >
            批量{{ hasDisabled ? "启用" : "禁用" }}
          </a-button>
        </a-popconfirm>

        <a-popconfirm :title="delComfirmText" @confirm="handleDelete(null)">
          <a-button
            type="primary"
            :loading="loadings.batchDelete"
            :disabled="!selectedItems.length"
            danger
          >
            批量删除
          </a-button>
        </a-popconfirm>
      </template>
      <template #action="{ row }">
        <a-button type="link" @click="handleEdit(row)">编辑</a-button>
        <a-popconfirm :title="delComfirmText" @confirm="handleDelete(row)">
          <a-button type="link" danger>删除</a-button>
        </a-popconfirm>
      </template>
    </table-template>
    <a-modal v-model:visible="isEditModalVisible" :footer="null" title="编辑关键词">
      <div v-if="isEditModalVisible">
        <keywords-form
          ref="keywordFormRef"
          :formData="currentEditRow"
          :isEdit="true"
          @successCallback="handleSaveSuccess"
        />
      </div>
    </a-modal>
  </div>
</template>
