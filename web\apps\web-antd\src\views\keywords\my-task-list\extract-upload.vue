<script lang="ts" setup>
import { ref } from "vue";
import { useUserStore } from "#/store/user";
const userStore = useUserStore();
const user_id = userStore.userid;
import { UploadOutlined } from "@ant-design/icons-vue";

// const handleChange = (info) => {
//   if (info.file.status !== "uploading") {
//     console.log(info.file, info.fileList);
//   }
//   if (info.file.status === "done") {
//     // message.success(`${info.file.name} file uploaded successfully`);
//   } else if (info.file.status === "error") {
//     // message.error(`${info.file.name} file upload failed.`);
//   }
// };
const fileList = ref([]);
</script>

<template>
  <!-- @change="uploadStatus" -->
  <a-upload
    v-model:file-list="fileList"
    accept=".word,.txt,.pdf"
    name="file"
    :action="`/manual/tool/file/upload?user_id=` + user_id"
  >
    <a-button type="primary">
      <upload-outlined></upload-outlined>
      点击选择本地文件
    </a-button>
    <p class="p-2">只能上传word/txt/pdf文件,且不超过10M</p>
  </a-upload>
</template>
