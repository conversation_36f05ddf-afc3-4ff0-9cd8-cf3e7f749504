import type { QueueCountDelayData } from '#/types/task';
import { defineStore } from 'pinia';
import { ref } from 'vue';

const useWebSocketStore = defineStore('websocket', () => {
  const deletedTaskIds = ref<number[]>([]);
  const releaseTask = ref<string>('');
  const queueCountDelayData = ref<QueueCountDelayData>({});
  const offlineSearchStatus = ref<boolean>(false);
  const offlineSearchItem = ref<string | null>(null);
  const setQueueCountDelayData = (data: QueueCountDelayData) => {
    queueCountDelayData.value = data;
    // badgeStore.setBadge(data);
  };

  const getQueueBadgeCount = (biz: string, queue: string) => {
    return queueCountDelayData.value[biz]?.[queue]?.num || 0;
  };
  const getBizBadgeCount = (biz: string) => {
    return Object.values(queueCountDelayData.value[biz] || {}).reduce(
      (acc, curr) => acc + curr.num,
      0,
    );
  };
  const setOfflineSearchStatus = (status: boolean) => {
    offlineSearchStatus.value = status;
  };

  const setOfflineSearchItem = (item: string | null) => {
    offlineSearchItem.value = item;
  };

  const $reset = () => {
    deletedTaskIds.value = [];
    releaseTask.value = '';
    queueCountDelayData.value = {};
  };
  return {
    deletedTaskIds,
    releaseTask,
    offlineSearchStatus,
    queueCountDelayData,
    offlineSearchItem,
    setQueueCountDelayData,
    $reset,
    getQueueBadgeCount,
    getBizBadgeCount,
    setOfflineSearchStatus,
    setOfflineSearchItem,
  };
}, {
  persist: true,
});

export default useWebSocketStore;
